#!/usr/bin/env python3
"""
网页资源下载分析工具
分析网页结构，寻找免费下载方法
"""

import requests
import re
import json
import time
from urllib.parse import urljoin, urlparse
from bs4 import BeautifulSoup
import argparse

class ResourceDownloadAnalyzer:
    def __init__(self, target_url):
        self.target_url = target_url
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        })
        
    def analyze_page_structure(self):
        """分析页面结构"""
        print(f"[+] 分析页面: {self.target_url}")
        
        try:
            response = self.session.get(self.target_url, timeout=15)
            response.raise_for_status()
            
            print(f"[+] 页面加载成功: {response.status_code}")
            
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 分析页面信息
            page_info = {
                'title': soup.title.string if soup.title else 'Unknown',
                'url': self.target_url,
                'content_length': len(response.text),
                'response_headers': dict(response.headers)
            }
            
            print(f"[+] 页面标题: {page_info['title']}")
            print(f"[+] 内容长度: {page_info['content_length']} 字符")
            
            return response.text, soup, page_info
            
        except Exception as e:
            print(f"[-] 页面分析失败: {e}")
            return None, None, None
    
    def find_download_links(self, html_content, soup):
        """查找下载链接"""
        print("[+] 搜索下载链接...")
        
        download_links = []
        
        # 1. 查找直接下载链接
        direct_patterns = [
            r'href=["\']([^"\']*\.(?:pdf|doc|docx|ppt|pptx|xls|xlsx|zip|rar|7z)[^"\']*)["\']',
            r'src=["\']([^"\']*\.(?:pdf|doc|docx|ppt|pptx|xls|xlsx|zip|rar|7z)[^"\']*)["\']',
        ]
        
        for pattern in direct_patterns:
            matches = re.findall(pattern, html_content, re.IGNORECASE)
            for match in matches:
                full_url = urljoin(self.target_url, match)
                download_links.append({
                    'type': 'direct',
                    'url': full_url,
                    'source': 'regex_pattern'
                })
        
        # 2. 查找下载按钮和链接
        download_elements = soup.find_all(['a', 'button'], 
            text=re.compile(r'下载|download|免费下载|立即下载', re.IGNORECASE))
        
        for element in download_elements:
            href = element.get('href')
            onclick = element.get('onclick')
            data_url = element.get('data-url')
            
            if href:
                full_url = urljoin(self.target_url, href)
                download_links.append({
                    'type': 'button_link',
                    'url': full_url,
                    'text': element.get_text().strip(),
                    'source': 'download_button'
                })
            
            if onclick:
                # 从onclick中提取URL
                url_match = re.search(r'["\']([^"\']*(?:download|file)[^"\']*)["\']', onclick)
                if url_match:
                    full_url = urljoin(self.target_url, url_match.group(1))
                    download_links.append({
                        'type': 'onclick',
                        'url': full_url,
                        'onclick': onclick,
                        'source': 'onclick_script'
                    })
            
            if data_url:
                full_url = urljoin(self.target_url, data_url)
                download_links.append({
                    'type': 'data_url',
                    'url': full_url,
                    'source': 'data_attribute'
                })
        
        # 3. 查找表单提交
        forms = soup.find_all('form')
        for form in forms:
            action = form.get('action')
            if action and ('download' in action.lower() or 'file' in action.lower()):
                full_url = urljoin(self.target_url, action)
                download_links.append({
                    'type': 'form',
                    'url': full_url,
                    'method': form.get('method', 'GET'),
                    'source': 'form_action'
                })
        
        print(f"[+] 发现 {len(download_links)} 个潜在下载链接")
        return download_links
    
    def analyze_javascript(self, html_content):
        """分析JavaScript中的下载逻辑"""
        print("[+] 分析JavaScript下载逻辑...")
        
        js_patterns = {
            'download_functions': r'function\s+(\w*download\w*)\s*\([^)]*\)\s*{([^}]+)}',
            'download_urls': r'["\']([^"\']*(?:download|file)[^"\']*\.(?:pdf|doc|docx|ppt|pptx|xls|xlsx|zip|rar|7z)[^"\']*)["\']',
            'ajax_calls': r'ajax\s*\(\s*{[^}]*url\s*:\s*["\']([^"\']*download[^"\']*)["\']',
            'fetch_calls': r'fetch\s*\(\s*["\']([^"\']*download[^"\']*)["\']',
        }
        
        js_data = {}
        
        for pattern_name, pattern in js_patterns.items():
            matches = re.findall(pattern, html_content, re.IGNORECASE | re.DOTALL)
            if matches:
                js_data[pattern_name] = matches
                print(f"[+] 发现 {pattern_name}: {len(matches)} 个")
        
        return js_data
    
    def check_free_access_methods(self, soup):
        """检查免费访问方法"""
        print("[+] 检查免费访问方法...")
        
        free_methods = []
        
        # 1. 查找免费试用或预览
        free_keywords = ['免费', '试用', '预览', 'free', 'trial', 'preview']
        for keyword in free_keywords:
            elements = soup.find_all(text=re.compile(keyword, re.IGNORECASE))
            if elements:
                free_methods.append({
                    'method': 'free_trial',
                    'keyword': keyword,
                    'count': len(elements)
                })
        
        # 2. 查找注册获取
        register_elements = soup.find_all(['a', 'button'], 
            text=re.compile(r'注册|register|sign.*up', re.IGNORECASE))
        if register_elements:
            free_methods.append({
                'method': 'registration',
                'elements': len(register_elements)
            })
        
        # 3. 查找分享获取
        share_elements = soup.find_all(['a', 'button'], 
            text=re.compile(r'分享|share|邀请', re.IGNORECASE))
        if share_elements:
            free_methods.append({
                'method': 'sharing',
                'elements': len(share_elements)
            })
        
        return free_methods
    
    def test_download_links(self, download_links):
        """测试下载链接的可访问性"""
        print("[+] 测试下载链接...")
        
        working_links = []
        
        for i, link in enumerate(download_links[:10]):  # 只测试前10个
            try:
                print(f"[+] 测试链接 {i+1}: {link['url']}")
                
                response = self.session.head(link['url'], timeout=10, allow_redirects=True)
                
                link_info = {
                    'url': link['url'],
                    'status': response.status_code,
                    'content_type': response.headers.get('Content-Type', 'Unknown'),
                    'content_length': response.headers.get('Content-Length', 'Unknown'),
                    'final_url': response.url,
                    'source': link['source']
                }
                
                if response.status_code == 200:
                    content_type = response.headers.get('Content-Type', '')
                    if any(file_type in content_type for file_type in 
                           ['application/', 'document', 'zip', 'pdf']):
                        working_links.append(link_info)
                        print(f"    ✅ 可访问文件: {content_type}")
                    else:
                        print(f"    ⚠️  可访问但非文件: {content_type}")
                elif response.status_code in [301, 302, 307, 308]:
                    print(f"    🔄 重定向到: {response.url}")
                    link_info['redirect'] = True
                    working_links.append(link_info)
                else:
                    print(f"    ❌ 不可访问: {response.status_code}")
                
            except Exception as e:
                print(f"    ❌ 测试失败: {e}")
        
        return working_links
    
    def find_alternative_sources(self, page_title):
        """查找替代资源来源"""
        print("[+] 查找替代资源来源...")
        
        # 提取关键词
        keywords = re.findall(r'[\u4e00-\u9fff]+', page_title)  # 中文关键词
        english_keywords = re.findall(r'[a-zA-Z]+', page_title)  # 英文关键词
        
        alternative_sources = [
            {
                'name': '百度文库',
                'url': f'https://wenku.baidu.com/search?word={"+".join(keywords[:3])}',
                'description': '可能有相同或类似资源'
            },
            {
                'name': '道客巴巴',
                'url': f'https://www.doc88.com/search.do?searchWord={"+".join(keywords[:3])}',
                'description': '文档分享平台'
            },
            {
                'name': '豆丁网',
                'url': f'https://www.docin.com/search.do?searchcat=2&searchType_banner=p&nkey={"+".join(keywords[:3])}',
                'description': '文档资源库'
            },
            {
                'name': '学科网免费资源',
                'url': 'https://www.zxxk.com/soft/free/',
                'description': '学科网免费资源专区'
            }
        ]
        
        return alternative_sources
    
    def generate_download_strategies(self, analysis_results):
        """生成下载策略"""
        strategies = []
        
        # 策略1: 直接下载
        if analysis_results.get('working_links'):
            strategies.append({
                'strategy': 'direct_download',
                'description': '直接下载可用链接',
                'links': analysis_results['working_links'],
                'success_rate': 'high'
            })
        
        # 策略2: 免费方法
        if analysis_results.get('free_methods'):
            strategies.append({
                'strategy': 'free_access',
                'description': '通过免费方法获取',
                'methods': analysis_results['free_methods'],
                'success_rate': 'medium'
            })
        
        # 策略3: 替代来源
        strategies.append({
            'strategy': 'alternative_sources',
            'description': '从其他平台获取相同资源',
            'sources': analysis_results['alternative_sources'],
            'success_rate': 'medium'
        })
        
        # 策略4: 技术方法
        if analysis_results.get('js_data'):
            strategies.append({
                'strategy': 'technical_analysis',
                'description': '通过技术分析获取下载链接',
                'data': analysis_results['js_data'],
                'success_rate': 'low'
            })
        
        return strategies
    
    def run_comprehensive_analysis(self):
        """运行全面分析"""
        print("="*60)
        print("🔍 网页资源下载分析")
        print("="*60)
        print(f"目标URL: {self.target_url}")
        print("="*60)
        
        # 1. 分析页面结构
        html_content, soup, page_info = self.analyze_page_structure()
        if not html_content:
            return
        
        # 2. 查找下载链接
        download_links = self.find_download_links(html_content, soup)
        
        # 3. 分析JavaScript
        js_data = self.analyze_javascript(html_content)
        
        # 4. 检查免费方法
        free_methods = self.check_free_access_methods(soup)
        
        # 5. 测试下载链接
        working_links = self.test_download_links(download_links)
        
        # 6. 查找替代来源
        alternative_sources = self.find_alternative_sources(page_info['title'])
        
        # 7. 整合分析结果
        analysis_results = {
            'page_info': page_info,
            'download_links': download_links,
            'working_links': working_links,
            'js_data': js_data,
            'free_methods': free_methods,
            'alternative_sources': alternative_sources
        }
        
        # 8. 生成下载策略
        strategies = self.generate_download_strategies(analysis_results)
        
        # 9. 输出结果
        self.display_results(analysis_results, strategies)
        
        # 10. 保存报告
        self.save_report(analysis_results, strategies)
        
        return analysis_results, strategies
    
    def display_results(self, analysis_results, strategies):
        """显示分析结果"""
        print("\n" + "="*60)
        print("📊 分析结果")
        print("="*60)
        
        # 显示工作链接
        if analysis_results['working_links']:
            print(f"\n✅ 发现 {len(analysis_results['working_links'])} 个可用下载链接:")
            for i, link in enumerate(analysis_results['working_links'], 1):
                print(f"  {i}. {link['url']}")
                print(f"     状态: {link['status']}, 类型: {link['content_type']}")
        
        # 显示免费方法
        if analysis_results['free_methods']:
            print(f"\n🆓 发现 {len(analysis_results['free_methods'])} 种免费获取方法:")
            for method in analysis_results['free_methods']:
                print(f"  - {method['method']}: {method}")
        
        # 显示下载策略
        print(f"\n🎯 推荐下载策略:")
        for i, strategy in enumerate(strategies, 1):
            print(f"  {i}. {strategy['description']} (成功率: {strategy['success_rate']})")
        
        # 显示替代来源
        print(f"\n🔄 替代资源来源:")
        for source in analysis_results['alternative_sources']:
            print(f"  - {source['name']}: {source['url']}")
    
    def save_report(self, analysis_results, strategies):
        """保存分析报告"""
        report = {
            'analysis_time': time.time(),
            'target_url': self.target_url,
            'analysis_results': analysis_results,
            'download_strategies': strategies
        }
        
        filename = f"download_analysis_{int(time.time())}.json"
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"\n[+] 分析报告已保存: {filename}")

def main():
    parser = argparse.ArgumentParser(description='网页资源下载分析工具')
    parser.add_argument('url', help='目标网页URL')
    
    args = parser.parse_args()
    
    print("🔍 网页资源下载分析工具")
    print("⚠️  仅用于合法的资源获取分析")
    print()
    
    analyzer = ResourceDownloadAnalyzer(args.url)
    analyzer.run_comprehensive_analysis()

if __name__ == "__main__":
    # 如果没有命令行参数，使用默认URL
    import sys
    if len(sys.argv) == 1:
        target_url = "https://www.zxxk.com/soft/40430046.html"
        analyzer = ResourceDownloadAnalyzer(target_url)
        analyzer.run_comprehensive_analysis()
    else:
        main()
