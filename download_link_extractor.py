#!/usr/bin/env python3
"""
下载链接提取工具 - 深度分析网页获取真实下载地址
专门用于提取隐藏的文件下载链接
"""

import requests
import re
import json
import base64
import time
from urllib.parse import urlparse, urljoin, unquote
from bs4 import BeautifulSoup
import argparse

class DownloadLinkExtractor:
    def __init__(self, target_url, secret_key):
        self.target_url = target_url
        self.secret_key = secret_key
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        })
        
    def extract_page_content(self):
        """提取页面内容"""
        print(f"[+] 正在分析目标页面: {self.target_url}")
        
        try:
            response = self.session.get(self.target_url, timeout=15)
            response.raise_for_status()
            
            print(f"[+] 页面加载成功: {response.status_code}")
            print(f"[+] 内容长度: {len(response.text)} 字符")
            
            return response.text, response.headers
            
        except Exception as e:
            print(f"[-] 页面加载失败: {e}")
            return None, None
    
    def find_download_patterns(self, html_content):
        """查找下载相关的模式"""
        print("[+] 搜索下载链接模式...")
        
        download_patterns = {
            'oss_links': r'https?://[^/]*\.oss[^/]*\.aliyuncs\.com/[^\s"\'<>]+',
            'direct_downloads': r'https?://[^\s"\'<>]*\.(docx?|pdf|zip|rar|7z|xlsx?|pptx?)',
            'api_endpoints': r'/api/[^\s"\'<>]*download[^\s"\'<>]*',
            'download_urls': r'["\']([^"\']*download[^"\']*)["\']',
            'file_urls': r'["\']([^"\']*\.(docx?|pdf|zip|rar|7z|xlsx?|pptx?)[^"\']*)["\']',
            'base64_data': r'data:application/[^;]+;base64,([A-Za-z0-9+/=]+)',
        }
        
        found_links = {}
        
        for pattern_name, pattern in download_patterns.items():
            matches = re.findall(pattern, html_content, re.IGNORECASE)
            if matches:
                found_links[pattern_name] = matches
                print(f"[+] 发现 {pattern_name}: {len(matches)} 个匹配")
                for match in matches[:3]:  # 只显示前3个
                    print(f"    - {match}")
                if len(matches) > 3:
                    print(f"    ... 还有 {len(matches) - 3} 个")
        
        return found_links
    
    def extract_javascript_data(self, html_content):
        """提取JavaScript中的数据"""
        print("[+] 分析JavaScript数据...")
        
        js_patterns = {
            'download_config': r'downloadConfig\s*[=:]\s*({[^}]+})',
            'file_info': r'fileInfo\s*[=:]\s*({[^}]+})',
            'paper_data': r'paperData\s*[=:]\s*({[^}]+})',
            'user_paper_info': r'userPaperInfo\s*[=:]\s*({[^}]+})',
            'download_url': r'downloadUrl\s*[=:]\s*["\']([^"\']+)["\']',
            'file_url': r'fileUrl\s*[=:]\s*["\']([^"\']+)["\']',
            'oss_url': r'ossUrl\s*[=:]\s*["\']([^"\']+)["\']',
        }
        
        js_data = {}
        
        for data_name, pattern in js_patterns.items():
            matches = re.findall(pattern, html_content, re.IGNORECASE | re.DOTALL)
            if matches:
                js_data[data_name] = matches
                print(f"[+] 发现 {data_name}: {len(matches)} 个")
                for match in matches:
                    if len(str(match)) > 100:
                        print(f"    - {str(match)[:100]}...")
                    else:
                        print(f"    - {match}")
        
        return js_data
    
    def analyze_api_calls(self, html_content):
        """分析可能的API调用"""
        print("[+] 分析API调用...")
        
        api_patterns = [
            r'fetch\(["\']([^"\']+)["\']',
            r'axios\.get\(["\']([^"\']+)["\']',
            r'ajax\(["\']([^"\']+)["\']',
            r'\$\.get\(["\']([^"\']+)["\']',
            r'\$\.post\(["\']([^"\']+)["\']',
            r'XMLHttpRequest.*open\(["\'][^"\']*["\'],\s*["\']([^"\']+)["\']',
        ]
        
        api_calls = []
        
        for pattern in api_patterns:
            matches = re.findall(pattern, html_content, re.IGNORECASE)
            api_calls.extend(matches)
        
        if api_calls:
            print(f"[+] 发现 {len(api_calls)} 个API调用:")
            for call in set(api_calls):  # 去重
                print(f"    - {call}")
        
        return list(set(api_calls))
    
    def test_download_endpoints(self, base_url):
        """测试可能的下载端点"""
        print("[+] 测试下载端点...")
        
        # 从目标URL提取基本信息
        paper_id = "1434582705370880"  # 从URL中提取
        
        test_endpoints = [
            f"/api/download?id={paper_id}&secret={self.secret_key}",
            f"/api/paper/download?paperId={paper_id}&secret={self.secret_key}",
            f"/download/{paper_id}?secret={self.secret_key}",
            f"/api/share/download?shareId={paper_id}&secret={self.secret_key}",
            f"/api/export?id={paper_id}&secret={self.secret_key}",
            f"/api/file/download?fileId={paper_id}&token={self.secret_key}",
            f"/share-paper/download/{paper_id}?secret={self.secret_key}",
        ]
        
        working_endpoints = []
        
        for endpoint in test_endpoints:
            try:
                test_url = urljoin(base_url, endpoint)
                print(f"[+] 测试: {test_url}")
                
                response = self.session.head(test_url, timeout=5, allow_redirects=True)
                
                if response.status_code == 200:
                    content_type = response.headers.get('Content-Type', '')
                    if 'application/' in content_type or 'download' in content_type:
                        working_endpoints.append({
                            'url': test_url,
                            'status': response.status_code,
                            'content_type': content_type,
                            'final_url': response.url
                        })
                        print(f"    ✅ 成功: {response.status_code} - {content_type}")
                        if response.url != test_url:
                            print(f"    🔄 重定向到: {response.url}")
                elif response.status_code == 302 or response.status_code == 301:
                    location = response.headers.get('Location', '')
                    working_endpoints.append({
                        'url': test_url,
                        'status': response.status_code,
                        'redirect_to': location
                    })
                    print(f"    🔄 重定向: {response.status_code} -> {location}")
                else:
                    print(f"    ❌ 失败: {response.status_code}")
                    
            except Exception as e:
                print(f"    ❌ 错误: {e}")
        
        return working_endpoints
    
    def extract_from_network_requests(self):
        """模拟浏览器行为，触发网络请求"""
        print("[+] 模拟浏览器行为...")
        
        # 首先访问页面，获取可能的CSRF token等
        try:
            response = self.session.get(self.target_url)
            
            # 查找可能触发下载的按钮或链接
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 查找下载按钮
            download_buttons = soup.find_all(['a', 'button'], 
                text=re.compile(r'下载|download', re.IGNORECASE))
            
            download_links = []
            
            for button in download_buttons:
                href = button.get('href')
                onclick = button.get('onclick')
                data_url = button.get('data-url')
                
                if href:
                    download_links.append(('href', href))
                if onclick:
                    download_links.append(('onclick', onclick))
                if data_url:
                    download_links.append(('data-url', data_url))
            
            print(f"[+] 发现 {len(download_links)} 个下载相关元素:")
            for link_type, link in download_links:
                print(f"    - {link_type}: {link}")
            
            return download_links
            
        except Exception as e:
            print(f"[-] 模拟浏览器行为失败: {e}")
            return []
    
    def construct_download_url(self):
        """根据已知信息构造下载URL"""
        print("[+] 尝试构造下载URL...")
        
        # 已知的URL模式
        known_pattern = "https://zujuanzuoye.oss-cn-beijing.aliyuncs.com/044b2d/2025%E5%B9%B46%E6%9C%8829%E6%97%A5%E9%AB%98%E4%B8%AD%E6%95%B0%E5%AD%A6%E4%BD%9C%E4%B8%9A_1434582705370880_878.docx"
        
        # 分析URL结构
        parsed = urlparse(known_pattern)
        
        print(f"[+] 已知URL结构分析:")
        print(f"    - 域名: {parsed.netloc}")
        print(f"    - 路径: {parsed.path}")
        print(f"    - 文件名: {parsed.path.split('/')[-1]}")
        
        # 尝试构造可能的URL变体
        base_oss_url = f"{parsed.scheme}://{parsed.netloc}"
        path_parts = parsed.path.split('/')
        
        possible_urls = []
        
        # 变体1: 不同的路径前缀
        for prefix in ['044b2d', '044b2c', '044b2e', 'files', 'documents']:
            url = f"{base_oss_url}/{prefix}/{path_parts[-1]}"
            possible_urls.append(url)
        
        # 变体2: 不同的文件ID
        paper_id = "1434582705370880"
        for suffix in ['878', '879', '880', '877']:
            filename = f"2025%E5%B9%B46%E6%9C%8829%E6%97%A5%E9%AB%98%E4%B8%AD%E6%95%B0%E5%AD%A6%E4%BD%9C%E4%B8%9A_{paper_id}_{suffix}.docx"
            url = f"{base_oss_url}/044b2d/{filename}"
            possible_urls.append(url)
        
        print(f"[+] 生成 {len(possible_urls)} 个可能的URL:")
        for url in possible_urls:
            print(f"    - {url}")
        
        return possible_urls
    
    def test_constructed_urls(self, urls):
        """测试构造的URL"""
        print("[+] 测试构造的URL...")
        
        working_urls = []
        
        for url in urls:
            try:
                print(f"[+] 测试: {url}")
                response = self.session.head(url, timeout=10)
                
                if response.status_code == 200:
                    content_length = response.headers.get('Content-Length', 'Unknown')
                    content_type = response.headers.get('Content-Type', 'Unknown')
                    
                    working_urls.append({
                        'url': url,
                        'status': response.status_code,
                        'content_length': content_length,
                        'content_type': content_type
                    })
                    
                    print(f"    ✅ 成功! 大小: {content_length} bytes, 类型: {content_type}")
                else:
                    print(f"    ❌ 失败: {response.status_code}")
                    
            except Exception as e:
                print(f"    ❌ 错误: {e}")
        
        return working_urls
    
    def run_full_extraction(self):
        """运行完整的提取流程"""
        print("="*60)
        print("🔍 下载链接提取工具")
        print("="*60)
        print(f"目标URL: {self.target_url}")
        print(f"密钥: {self.secret_key}")
        print("="*60)
        
        # 1. 提取页面内容
        html_content, headers = self.extract_page_content()
        if not html_content:
            return
        
        print()
        
        # 2. 查找下载模式
        download_links = self.find_download_patterns(html_content)
        print()
        
        # 3. 提取JavaScript数据
        js_data = self.extract_javascript_data(html_content)
        print()
        
        # 4. 分析API调用
        api_calls = self.analyze_api_calls(html_content)
        print()
        
        # 5. 测试下载端点
        base_url = f"{urlparse(self.target_url).scheme}://{urlparse(self.target_url).netloc}"
        working_endpoints = self.test_download_endpoints(base_url)
        print()
        
        # 6. 模拟浏览器行为
        browser_links = self.extract_from_network_requests()
        print()
        
        # 7. 构造可能的URL
        constructed_urls = self.construct_download_url()
        print()
        
        # 8. 测试构造的URL
        working_constructed = self.test_constructed_urls(constructed_urls)
        print()
        
        # 9. 总结结果
        print("="*60)
        print("📊 提取结果总结")
        print("="*60)
        
        if download_links:
            print("🔗 发现的下载链接:")
            for category, links in download_links.items():
                print(f"  {category}: {len(links)} 个")
        
        if working_endpoints:
            print("✅ 可用的API端点:")
            for endpoint in working_endpoints:
                print(f"  - {endpoint['url']}")
                if 'redirect_to' in endpoint:
                    print(f"    重定向到: {endpoint['redirect_to']}")
        
        if working_constructed:
            print("🎯 成功的构造URL:")
            for url_info in working_constructed:
                print(f"  - {url_info['url']}")
                print(f"    大小: {url_info['content_length']} bytes")
                print(f"    类型: {url_info['content_type']}")
        
        # 10. 生成最终报告
        self.generate_report(download_links, js_data, api_calls, working_endpoints, working_constructed)
    
    def generate_report(self, download_links, js_data, api_calls, working_endpoints, working_constructed):
        """生成详细报告"""
        report = {
            'target_url': self.target_url,
            'secret_key': self.secret_key,
            'timestamp': time.time(),
            'download_links': download_links,
            'javascript_data': js_data,
            'api_calls': api_calls,
            'working_endpoints': working_endpoints,
            'working_constructed_urls': working_constructed
        }
        
        with open('download_extraction_report.json', 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"[+] 详细报告已保存到: download_extraction_report.json")

def main():
    parser = argparse.ArgumentParser(description='下载链接提取工具')
    parser.add_argument('--url', default='https://zujuan.xkw.com/share-paper/1434582705370880.html', help='目标URL')
    parser.add_argument('--secret', default='5a2b2d1b9babecc1dcaa447401561f13', help='密钥')
    
    args = parser.parse_args()
    
    extractor = DownloadLinkExtractor(
        target_url=f"{args.url}?secret={args.secret}",
        secret_key=args.secret
    )
    
    extractor.run_full_extraction()

if __name__ == "__main__":
    main()
