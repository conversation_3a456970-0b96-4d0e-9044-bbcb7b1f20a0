#!/usr/bin/env python3
"""
文件后缀分析工具 - 分析878等后缀数字的生成规律
专门用于分析文件命名中的数字后缀算法
"""

import requests
import re
import hashlib
import time
import json
from datetime import datetime
from urllib.parse import urlparse
import argparse

class SuffixAnalyzer:
    def __init__(self, target_url, secret_key, paper_id):
        self.target_url = target_url
        self.secret_key = secret_key
        self.paper_id = paper_id
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        
    def analyze_page_for_clues(self):
        """分析页面寻找878生成的线索"""
        print("[+] 分析页面内容寻找878生成线索...")
        
        try:
            response = self.session.get(self.target_url)
            html_content = response.text
            
            # 查找可能相关的数字模式
            patterns = {
                '878相关': r'878',
                '三位数字': r'\b\d{3}\b',
                '时间戳相关': r'\b1[67]\d{8,10}\b',
                'ID相关': r'id["\']?\s*[:=]\s*["\']?(\d+)',
                '版本号': r'version["\']?\s*[:=]\s*["\']?(\d+)',
                '序号': r'(index|seq|num)["\']?\s*[:=]\s*["\']?(\d+)',
                '哈希相关': r'[a-f0-9]{6,}',
            }
            
            found_clues = {}
            
            for pattern_name, pattern in patterns.items():
                matches = re.findall(pattern, html_content, re.IGNORECASE)
                if matches:
                    found_clues[pattern_name] = matches
                    print(f"[+] 发现 {pattern_name}: {len(matches)} 个")
                    # 只显示前5个匹配
                    for match in matches[:5]:
                        print(f"    - {match}")
                    if len(matches) > 5:
                        print(f"    ... 还有 {len(matches) - 5} 个")
            
            return found_clues, html_content
            
        except Exception as e:
            print(f"[-] 页面分析失败: {e}")
            return {}, ""
    
    def test_hash_algorithms(self):
        """测试各种哈希算法生成878"""
        print("[+] 测试哈希算法生成878...")
        
        # 可能的输入数据
        test_inputs = [
            self.paper_id,
            self.secret_key,
            f"{self.paper_id}_{self.secret_key}",
            f"{self.secret_key}_{self.paper_id}",
            "2025年6月29日",
            "20250629",
            f"{self.paper_id}_20250629",
            f"20250629_{self.paper_id}",
            str(int(time.time())),
            "1434582705370880",
        ]
        
        hash_functions = {
            'MD5': hashlib.md5,
            'SHA1': hashlib.sha1,
            'SHA256': hashlib.sha256,
        }
        
        results = []
        
        for input_data in test_inputs:
            for hash_name, hash_func in hash_functions.items():
                try:
                    # 计算哈希
                    hash_obj = hash_func(str(input_data).encode('utf-8'))
                    hash_hex = hash_obj.hexdigest()
                    
                    # 尝试不同的提取方法
                    extraction_methods = {
                        '前3位': hash_hex[:3],
                        '后3位': hash_hex[-3:],
                        '中间3位': hash_hex[len(hash_hex)//2-1:len(hash_hex)//2+2],
                        '十进制前3位': str(int(hash_hex[:8], 16))[:3],
                        '十进制后3位': str(int(hash_hex[-8:], 16))[-3:],
                    }
                    
                    for method_name, extracted in extraction_methods.items():
                        if extracted == '878':
                            result = {
                                'input': input_data,
                                'hash_type': hash_name,
                                'method': method_name,
                                'hash': hash_hex,
                                'extracted': extracted
                            }
                            results.append(result)
                            print(f"[!] 🎯 找到匹配! {input_data} -> {hash_name} -> {method_name} -> 878")
                            print(f"    完整哈希: {hash_hex}")
                
                except Exception as e:
                    continue
        
        return results
    
    def test_mathematical_operations(self):
        """测试数学运算生成878"""
        print("[+] 测试数学运算生成878...")
        
        # 从paper_id和secret_key中提取数字
        paper_id_int = int(self.paper_id)
        secret_numbers = re.findall(r'\d+', self.secret_key)
        
        results = []
        
        # 测试各种数学运算
        operations = [
            ('模运算1000', lambda x: x % 1000),
            ('模运算999', lambda x: x % 999),
            ('除法取整', lambda x: x // 1000000000000),
            ('位运算', lambda x: x & 0xFFF),
            ('字符串长度相关', lambda x: (x % 10000) + len(str(x))),
        ]
        
        test_values = [
            paper_id_int,
            int(self.secret_key[:8], 16) if len(self.secret_key) >= 8 else 0,
            int(self.secret_key[-8:], 16) if len(self.secret_key) >= 8 else 0,
        ]
        
        for value in test_values:
            for op_name, operation in operations:
                try:
                    result = operation(value)
                    if str(result) == '878' or str(result)[-3:] == '878':
                        match_info = {
                            'input': value,
                            'operation': op_name,
                            'result': result,
                            'match': '878'
                        }
                        results.append(match_info)
                        print(f"[!] 🎯 数学运算匹配! {value} -> {op_name} -> {result}")
                except:
                    continue
        
        return results
    
    def test_time_based_generation(self):
        """测试基于时间的生成方法"""
        print("[+] 测试时间相关的生成方法...")
        
        # 当前时间和可能的时间
        current_time = int(time.time())
        possible_times = [
            current_time,
            1719676800,  # 2024-06-29的时间戳
            1751212800,  # 2025-06-29的时间戳
        ]
        
        results = []
        
        for timestamp in possible_times:
            # 测试不同的时间提取方法
            time_methods = {
                '时间戳后3位': str(timestamp)[-3:],
                '时间戳模1000': str(timestamp % 1000),
                '小时分钟': datetime.fromtimestamp(timestamp).strftime('%H%M')[-3:],
                '日期相关': datetime.fromtimestamp(timestamp).strftime('%d%m')[-3:],
            }
            
            for method_name, extracted in time_methods.items():
                if extracted == '878':
                    result = {
                        'timestamp': timestamp,
                        'datetime': datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M:%S'),
                        'method': method_name,
                        'extracted': extracted
                    }
                    results.append(result)
                    print(f"[!] 🎯 时间匹配! {timestamp} -> {method_name} -> 878")
        
        return results
    
    def test_sequential_patterns(self):
        """测试序列模式"""
        print("[+] 测试序列和计数模式...")
        
        # 可能的序列起点
        base_numbers = [
            870, 871, 872, 873, 874, 875, 876, 877, 879, 880,
            0, 1, 100, 500, 800
        ]
        
        results = []
        
        # 测试简单的序列
        for base in base_numbers:
            for offset in range(10):
                if base + offset == 878:
                    result = {
                        'base': base,
                        'offset': offset,
                        'result': 878,
                        'pattern': f'{base} + {offset}'
                    }
                    results.append(result)
                    print(f"[!] 🎯 序列匹配! {base} + {offset} = 878")
        
        return results
    
    def analyze_paper_metadata(self, html_content):
        """分析试卷元数据寻找878的来源"""
        print("[+] 分析试卷元数据...")
        
        # 查找可能的元数据
        metadata_patterns = {
            'question_count': r'(\d+).*?题',
            'page_count': r'(\d+).*?页',
            'score': r'(\d+).*?分',
            'time_limit': r'(\d+).*?分钟',
            'difficulty': r'difficulty["\']?\s*[:=]\s*["\']?(\d+)',
            'version': r'version["\']?\s*[:=]\s*["\']?(\d+)',
        }
        
        metadata = {}
        
        for key, pattern in metadata_patterns.items():
            matches = re.findall(pattern, html_content, re.IGNORECASE)
            if matches:
                metadata[key] = matches
                print(f"[+] {key}: {matches}")
        
        # 检查是否有数字组合能得到878
        all_numbers = []
        for values in metadata.values():
            all_numbers.extend([int(v) for v in values if v.isdigit()])
        
        results = []
        
        # 测试数字组合
        for i, num1 in enumerate(all_numbers):
            for j, num2 in enumerate(all_numbers):
                if i != j:
                    combinations = [
                        ('加法', num1 + num2),
                        ('减法', abs(num1 - num2)),
                        ('乘法', num1 * num2),
                        ('连接', int(f"{num1}{num2}") if len(f"{num1}{num2}") <= 10 else 0),
                    ]
                    
                    for op_name, result in combinations:
                        if str(result) == '878' or str(result).endswith('878'):
                            match_info = {
                                'operation': f'{num1} {op_name} {num2}',
                                'result': result,
                                'match': '878'
                            }
                            results.append(match_info)
                            print(f"[!] 🎯 元数据组合匹配! {num1} {op_name} {num2} = {result}")
        
        return results, metadata
    
    def test_file_system_patterns(self):
        """测试文件系统相关的模式"""
        print("[+] 测试文件系统模式...")
        
        # 可能的文件系统相关数字
        fs_tests = {
            'inode模拟': lambda: hash(self.paper_id) % 1000,
            '文件大小相关': lambda: 1868307 % 1000,  # 已知文件大小
            '创建时间': lambda: int(time.time()) % 1000,
            'CRC校验': lambda: abs(hash(f"{self.paper_id}_{self.secret_key}")) % 1000,
        }
        
        results = []
        
        for test_name, test_func in fs_tests.items():
            try:
                result = test_func()
                if str(result) == '878':
                    match_info = {
                        'test': test_name,
                        'result': result,
                        'match': '878'
                    }
                    results.append(match_info)
                    print(f"[!] 🎯 文件系统匹配! {test_name} -> {result}")
            except:
                continue
        
        return results
    
    def run_comprehensive_analysis(self):
        """运行全面分析"""
        print("="*60)
        print("🔍 878后缀生成算法分析")
        print("="*60)
        print(f"Paper ID: {self.paper_id}")
        print(f"Secret Key: {self.secret_key}")
        print(f"目标后缀: 878")
        print("="*60)
        
        all_results = {}
        
        # 1. 页面分析
        clues, html_content = self.analyze_page_for_clues()
        all_results['page_clues'] = clues
        
        # 2. 哈希算法测试
        hash_results = self.test_hash_algorithms()
        all_results['hash_matches'] = hash_results
        
        # 3. 数学运算测试
        math_results = self.test_mathematical_operations()
        all_results['math_matches'] = math_results
        
        # 4. 时间相关测试
        time_results = self.test_time_based_generation()
        all_results['time_matches'] = time_results
        
        # 5. 序列模式测试
        seq_results = self.test_sequential_patterns()
        all_results['sequence_matches'] = seq_results
        
        # 6. 元数据分析
        metadata_results, metadata = self.analyze_paper_metadata(html_content)
        all_results['metadata_matches'] = metadata_results
        all_results['metadata'] = metadata
        
        # 7. 文件系统模式
        fs_results = self.test_file_system_patterns()
        all_results['filesystem_matches'] = fs_results
        
        # 8. 总结结果
        self.summarize_results(all_results)
        
        # 9. 保存详细报告
        with open('suffix_878_analysis.json', 'w', encoding='utf-8') as f:
            json.dump(all_results, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"[+] 详细分析报告已保存到: suffix_878_analysis.json")
        
        return all_results
    
    def summarize_results(self, results):
        """总结分析结果"""
        print("\n" + "="*60)
        print("📊 878生成算法分析总结")
        print("="*60)
        
        total_matches = 0
        
        for category, matches in results.items():
            if isinstance(matches, list) and matches:
                total_matches += len(matches)
                print(f"\n🎯 {category}: {len(matches)} 个匹配")
                for match in matches:
                    if isinstance(match, dict):
                        print(f"  - {match}")
        
        if total_matches == 0:
            print("\n❌ 未找到878的确切生成方法")
            print("\n💡 可能的原因:")
            print("  1. 使用了更复杂的算法")
            print("  2. 涉及服务器端的动态数据")
            print("  3. 基于用户特定的信息")
            print("  4. 随机生成的序列号")
            print("  5. 数据库自增ID的一部分")
        else:
            print(f"\n✅ 总共找到 {total_matches} 个可能的生成方法")
            print("\n🔍 建议进一步验证最有可能的方法")

def main():
    parser = argparse.ArgumentParser(description='878后缀生成算法分析工具')
    parser.add_argument('--url', default='https://zujuan.xkw.com/share-paper/1434582705370880.html', help='目标URL')
    parser.add_argument('--secret', default='5a2b2d1b9babecc1dcaa447401561f13', help='密钥')
    parser.add_argument('--paper-id', default='1434582705370880', help='试卷ID')
    
    args = parser.parse_args()
    
    analyzer = SuffixAnalyzer(
        target_url=f"{args.url}?secret={args.secret}",
        secret_key=args.secret,
        paper_id=args.paper_id
    )
    
    analyzer.run_comprehensive_analysis()

if __name__ == "__main__":
    main()
